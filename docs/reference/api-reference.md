# 📚 API Reference

This reference provides detailed information about the core classes and methods in the **Hybrid Optimization Macroeconomic Framework**.

## 🏗️ Core Classes

### **YemenDataHandler**
**Location**: `src/data_handler.py`

Manages macroeconomic data loading, validation, and updates.

```python
class YemenDataHandler:
    def __init__(self, csv_path: str)
    def get_variable(self, variable_code: str, years: List[int] = None) -> Dict[int, float]
    def update_variable(self, variable_code: str, year: int, value: float) -> None
    def save_data(self, output_path: str) -> None
```

**Key Methods**:
- `get_variable()` - Retrieve variable values for specified years
- `update_variable()` - Update single variable value
- `save_data()` - Export adjusted data to CSV

### **IMFTargetProcessor**
**Location**: `src/target_processor.py`

Processes IMF program targets from YAML configuration.

```python
class IMFTargetProcessor:
    def __init__(self, targets_path: str, rules_path: str)
    def get_gdp_targets(self, years: List[int]) -> Dict[int, float]
    def get_fiscal_targets(self, years: List[int]) -> Dict[str, Dict[int, float]]
    def get_trade_targets(self, years: List[int]) -> Dict[str, Dict[int, float]]
```

**Key Methods**:
- `get_gdp_targets()` - GDP nominal targets in USD billions
- `get_fiscal_targets()` - Revenue and expenditure targets as % GDP
- `get_trade_targets()` - Export and import targets in USD billions

### **IdentityValidator**
**Location**: `src/identity_validator.py`

Validates economic identities and accounting relationships.

```python
class IdentityValidator:
    def __init__(self, data_handler: YemenDataHandler, tolerance: float = 5.0)
    def validate_all_identities(self, years: List[int]) -> bool
    def validate_gdp_expenditure_identity(self, years: List[int]) -> bool
    def validate_bop_identity(self, years: List[int]) -> bool
    def validate_cross_sector_trade_consistency(self, years: List[int]) -> bool
```

**Key Methods**:
- `validate_all_identities()` - Comprehensive validation of all 14 identities
- `validate_gdp_expenditure_identity()` - GDP = C + I + G + (X-M) + SD
- `validate_bop_identity()` - CA + KA + FA + E&O = 0
- `validate_cross_sector_trade_consistency()` - MFMOD compliance check

### **IdentityPreservingAligner**
**Location**: `src/identity_preserving_aligner.py`

Core optimization engine that preserves economic identities.

```python
class IdentityPreservingAligner:
    def __init__(self, data_handler: YemenDataHandler, target_processor: IMFTargetProcessor, tolerance: float = 5.0)
    def align_to_targets(self, years: List[int]) -> AlignmentResult
    def _goal_seek(self, year: int, target_variable: str, target_value: float, instrument_variable: str) -> bool
```

**Key Methods**:
- `align_to_targets()` - Main optimization method
- `_goal_seek()` - Iterative adjustment to meet specific targets

### **MultiStageOptimizer**
**Location**: `src/multi_stage_optimizer.py`

Implements 7-stage optimization process.

```python
class MultiStageOptimizer:
    def __init__(self, aligner: IdentityPreservingAligner)
    def optimize(self, years: List[int]) -> Dict[str, Any]
    def _stage_1_gdp_targets(self, years: List[int]) -> bool
    def _stage_3_trade_balance(self, years: List[int]) -> bool
```

**Key Methods**:
- `optimize()` - Execute complete 7-stage optimization
- `_stage_1_gdp_targets()` - Align GDP targets via investment
- `_stage_3_trade_balance()` - Align import/export targets

### **HybridIntegration**
**Location**: `src/hybrid_integration.py`

Orchestrates hybrid optimization with intelligent fallback.

```python
class HybridIntegration:
    def __init__(self, data_handler: YemenDataHandler, target_processor: IMFTargetProcessor)
    def run_integrated_optimization(self, years: List[int]) -> Dict[str, Any]
    def _run_current_system(self, years: List[int]) -> Dict[str, Any]
    def _run_hybrid_optimization(self, years: List[int]) -> Dict[str, Any]
```

**Key Methods**:
- `run_integrated_optimization()` - Main hybrid optimization entry point
- `_run_current_system()` - Execute proven optimization system
- `_run_hybrid_optimization()` - Test DeepSeek mathematical insights

### **TradeConsistencyResolver**
**Location**: `src/trade_consistency_resolver.py`

Ensures MFMOD compliance for trade data consistency.

```python
class TradeConsistencyResolver:
    def __init__(self, data_handler: YemenDataHandler, config: TradeConsistencyConfig = None)
    def resolve_trade_consistency(self, years: List[int]) -> Dict[str, Any]
    def _enforce_mfmod_compliance(self, year: int, inconsistency: Dict[str, Any]) -> Dict[str, Any]
```

**Key Methods**:
- `resolve_trade_consistency()` - Main MFMOD compliance enforcement
- `_enforce_mfmod_compliance()` - Adjust BOP values to match NA exactly

## 🎯 Key Data Structures

### **AlignmentResult**
```python
@dataclass
class AlignmentResult:
    success: bool
    target_achievement: Dict[str, Dict[int, float]]
    identity_validation: Dict[str, bool]
    adjustments_made: List[AdjustmentRecord]
    final_data: pd.DataFrame
```

### **AdjustmentRecord**
```python
@dataclass
class AdjustmentRecord:
    variable: str
    year: int
    original_value: float
    adjusted_value: float
    adjustment_type: str
    reason: str
```

### **TradeConsistencyConfig**
```python
@dataclass
class TradeConsistencyConfig:
    export_consistency_tolerance: float = 0.15
    import_consistency_tolerance: float = 0.15
    prioritize_bop_data: bool = True
    allow_statistical_discrepancy: bool = True
    max_discrepancy_pct_gdp: float = 0.02
```

## 🔧 Usage Examples

### **Basic Optimization**
```python
from src.data_handler import YemenDataHandler
from src.target_processor import IMFTargetProcessor
from src.hybrid_integration import HybridIntegration

# Initialize components
data_handler = YemenDataHandler('data/yemen_macro_data.csv')
target_processor = IMFTargetProcessor('config/imf_targets.yaml', 'config/adjustment_rules.yaml')
hybrid_integration = HybridIntegration(data_handler, target_processor)

# Run optimization
years = [2022, 2023, 2024, 2025]
result = hybrid_integration.run_integrated_optimization(years)

# Check results
if result['success']:
    print("✅ Optimization successful")
    print(f"Identity validation: {result['identity_validation']}")
    print(f"Target achievement: {result['target_achievement']}")
```

### **Identity Validation Only**
```python
from src.identity_validator import IdentityValidator

# Initialize validator
validator = IdentityValidator(data_handler, tolerance=5.0)

# Validate all identities
years = [2022, 2023, 2024, 2025]
all_valid = validator.validate_all_identities(years)

# Check specific identity
gdp_valid = validator.validate_gdp_expenditure_identity(years)
bop_valid = validator.validate_bop_identity(years)
trade_valid = validator.validate_cross_sector_trade_consistency(years)

print(f"All identities valid: {all_valid}")
print(f"GDP expenditure identity: {gdp_valid}")
print(f"BOP identity: {bop_valid}")
print(f"Trade consistency: {trade_valid}")
```

### **MFMOD Compliance Check**
```python
from src.trade_consistency_resolver import TradeConsistencyResolver, TradeConsistencyConfig

# Configure for MFMOD compliance
config = TradeConsistencyConfig(
    export_consistency_tolerance=0.001,  # 0.1% for MFMOD
    import_consistency_tolerance=0.001,  # 0.1% for MFMOD
    prioritize_bop_data=False,           # Adjust BOP to match NA
    allow_statistical_discrepancy=True
)

# Initialize resolver
resolver = TradeConsistencyResolver(data_handler, config)

# Enforce MFMOD compliance
years = [2022, 2023, 2024, 2025]
result = resolver.resolve_trade_consistency(years)

print(f"MFMOD compliance: {result['success']}")
print(f"Adjustments made: {len(result['adjustments_made'])}")
```

### **Custom Target Configuration**
```python
# Load custom targets
custom_targets = IMFTargetProcessor('config/custom_targets.yaml', 'config/adjustment_rules.yaml')

# Get specific targets
gdp_targets = custom_targets.get_gdp_targets([2024, 2025])
fiscal_targets = custom_targets.get_fiscal_targets([2024, 2025])

print(f"GDP targets: {gdp_targets}")
print(f"Fiscal targets: {fiscal_targets}")
```

## 📊 Variable Codes Reference

### **GDP Variables**
- `YEMNYGDPMKTPCN` - GDP Nominal (LCU millions)
- `YEMNYGDPMKTPKN` - GDP Real (LCU millions, constant prices)
- `YEMNYGDPMKTPCD` - GDP Deflator (index, base year = 100)

### **Fiscal Variables**
- `YEMGGREVTOTLCN` - Total Government Revenue (LCU millions)
- `YEMGGEXPTOTLCN` - Total Government Expenditure (LCU millions)
- `YEMGGBALOVRLCN` - Overall Fiscal Balance (LCU millions)

### **External Sector Variables**
- `YEMNEEXPGNFSCN` - Exports of Goods and Services (LCU millions)
- `YEMNEIMPGNFSCN` - Imports of Goods and Services (LCU millions)
- `YEMPANUSATLS` - Exchange Rate (LCU per USD, period average)

### **BOP Variables**
- `YEMBXGSRMRCHCD` - Exports of Goods (USD millions)
- `YEMBXGSRNFSVCD` - Exports of Services (USD millions)
- `YEMBMGSRMRCHCD` - Imports of Goods (USD millions)
- `YEMBMGSRNFSVCD` - Imports of Services (USD millions)

## 🚀 Advanced Usage

### **Custom Optimization Strategy**
```python
# Create custom aligner with specific tolerance
aligner = IdentityPreservingAligner(data_handler, target_processor, tolerance=1.0)

# Run with custom years
result = aligner.align_to_targets([2023, 2024])

# Access detailed results
for adjustment in result.adjustments_made:
    print(f"{adjustment.variable} ({adjustment.year}): {adjustment.original_value} → {adjustment.adjusted_value}")
```

### **Performance Monitoring**
```python
import time

start_time = time.time()
result = hybrid_integration.run_integrated_optimization(years)
execution_time = time.time() - start_time

print(f"Execution time: {execution_time:.2f} seconds")
print(f"Identities preserved: {sum(result['identity_validation'].values())}/14")
print(f"Average target achievement: {np.mean([v for targets in result['target_achievement'].values() for v in targets.values()]):.1%}")
```

## 🏆 Best Practices

### **Error Handling**
```python
try:
    result = hybrid_integration.run_integrated_optimization(years)
    if not result['success']:
        print(f"Optimization failed: {result.get('error', 'Unknown error')}")
except Exception as e:
    print(f"System error: {e}")
```

### **Data Validation**
```python
# Validate data before optimization
required_variables = ['YEMNYGDPMKTPCN', 'YEMPANUSATLS', 'YEMNEEXPGNFSCN']
for var in required_variables:
    values = data_handler.get_variable(var, years)
    if not all(v > 0 for v in values.values()):
        print(f"Warning: {var} has zero or negative values")
```

### **Result Validation**
```python
# Validate results after optimization
if result['success']:
    # Check identity preservation
    identity_score = sum(result['identity_validation'].values()) / len(result['identity_validation'])
    if identity_score < 0.9:
        print(f"Warning: Low identity preservation score: {identity_score:.1%}")
    
    # Check target achievement
    achievements = [v for targets in result['target_achievement'].values() for v in targets.values()]
    avg_achievement = np.mean(achievements)
    if avg_achievement < 0.95 or avg_achievement > 1.05:
        print(f"Warning: Target achievement outside normal range: {avg_achievement:.1%}")
```

---

*For more detailed examples, see the [Implementation Guides](../implementation/) and [Technical Documentation](../technical/)*
