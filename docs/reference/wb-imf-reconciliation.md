# WB-IMF Reconciliation Framework

## Overview

This document explains the reconciliation framework used to handle methodological differences between World Bank (WB) and International Monetary Fund (IMF) data for Yemen's macroeconomic alignment.

## The Mathematical Challenge

When aligning World Bank data to IMF targets, we face a fundamental mathematical impossibility:
- IMF targets imply specific economic relationships (e.g., fiscal balance as % of GDP)
- These relationships may be inconsistent with the underlying World Bank data structure
- Preserving all economic identities while achieving all targets is mathematically impossible

## Example: 2023 Analysis

Consider the 2023 targets:
- GDP target: $19.4B
- Fiscal balance target: -5.633% of GDP = -$1.09B
- Current account target: -12.157% of GDP = -$2.36B

From the savings-investment identity: (S-I) = (G-T) + (X-M)
- Where (G-T) = -fiscal balance = $1.09B
- And (X-M) = current account = -$2.36B
- This implies: Private savings - Investment = -$1.27B

This means private investment must exceed private savings by $1.27B, which must be financed somehow. The IMF targets assume financing mechanisms or data structures that differ from the World Bank's accounting framework.

## Statistical Discrepancy as Reconciliation

Statistical discrepancies in this framework serve as **reconciliation items** between two valid but different methodological approaches:

### 1. GDP Statistical Discrepancy
- **Purpose**: Reconciles GDP measurement differences
- **Limit**: Up to 10% of GDP for crisis economies
- **Interpretation**: Not a data error, but a methodology difference

### 2. Fiscal Statistical Discrepancy
- **Purpose**: Documents fiscal accounting differences
- **Limit**: Up to 10% of GDP
- **Interpretation**: Different treatment of off-budget items, quasi-fiscal operations

### 3. BOP Errors & Omissions
- **Purpose**: Captures unrecorded transactions
- **Limit**: Up to 20% of GDP for crisis economies
- **Interpretation**: Common in conflict-affected states with informal economies

## Implementation

The framework is implemented through:

1. **Configuration** (`adjustment_rules.yaml`):
   ```yaml
   wb_imf_reconciliation:
     crisis_economy_discrepancy_limit: 0.10  # 10% of GDP
     documentation: "Discrepancies arise from different methodologies"
     priority_order: "GDP targets take precedence over ratio targets"
   ```

2. **Validation** (`identity_validator.py`):
   - Accepts up to 10% GDP discrepancy for crisis economies
   - Documents discrepancies as reconciliation items

3. **Alignment** (`identity_preserving_aligner.py`):
   - Calculates required discrepancies
   - Applies them within acceptable bounds
   - Prioritizes GDP targets over ratio targets

## Priority Framework

When conflicts arise between targets:
1. **GDP nominal** (highest priority - critical for debt sustainability)
2. **Fiscal balance** (IMF program conditionality)
3. **Government revenue** (fiscal sustainability)
4. **Government expenditure**
5. **Imports** (trade financing needs)
6. **Inflation** (monetary stability)
7. **Exports** (lower priority given oil export halt)

## Transparency and Reporting

The alignment process generates detailed reports showing:
- Which targets were achieved (✅)
- Which targets could not be achieved due to mathematical constraints (⚠️)
- Size and nature of reconciliation items used
- Identity validation results

## Conclusion

This framework acknowledges that perfect alignment between WB and IMF data is mathematically impossible while preserving economic identities. Instead, it provides a transparent, documented approach to reconciliation that:
- Preserves the integrity of economic identities
- Achieves critical targets (especially GDP)
- Documents methodology differences transparently
- Maintains audit trails for all adjustments

The larger discrepancy allowances for crisis economies reflect the reality of data collection challenges in conflict-affected states while maintaining analytical rigor.