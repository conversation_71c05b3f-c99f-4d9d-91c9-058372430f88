# ⚙️ Configuration Guide

This guide explains how to configure the **Hybrid Optimization Macroeconomic Framework** for different scenarios, countries, and target sets.

## 📁 Configuration Files

### **Primary Configuration**
- **`config/imf_targets.yaml`** - IMF program targets
- **`config/adjustment_rules.yaml`** - Optimization rules and constraints
- **`data/yemen_macro_data.csv`** - World Bank macroeconomic data

### **System Parameters**
- **Crisis tolerance levels** - Built into the system
- **Optimization convergence** - Automatic tuning
- **Identity validation thresholds** - Predefined standards

## 🎯 IMF Targets Configuration

### **File Structure** (`config/imf_targets.yaml`)
```yaml
# Real Sector Targets
real_sector:
  gdp_nominal_usd_billions:
    2022: 21.6
    2023: 24.0
    2024: 29.4
    2025: 40.1

# Fiscal Sector Targets  
fiscal_sector:
  revenue_percent_gdp:
    2023: 6.1
    2024: 6.4
    2025: 5.9
  
  expenditure_percent_gdp:
    2022: 11.8
    2023: 11.8
    2024: 8.9
    2025: 9.6

  fiscal_balance_percent_gdp:
    2022: -5.9
    2023: -5.7
    2024: -2.5
    2025: -3.7

# External Sector Targets
external_sector:
  exports_total_usd_billions:
    2022: 2.7
    2023: 2.7
    2024: 2.7
    2025: 2.8

  imports_usd_billions:
    2022: 16.7
    2023: 19.3
    2024: 28.2
    2025: 33.9

  current_account_percent_gdp:
    2022: -12.9
    2023: -12.2
    2024: -15.4
    2025: -18.5

# Monetary Sector Targets
monetary_sector:
  inflation_percent:
    2022: 50.0
    2023: 25.0
    2024: 15.0
    2025: 10.0
```

### **Target Types**

**1. Absolute Targets**
```yaml
gdp_nominal_usd_billions:
  2025: 40.1  # $40.1 billion target
```

**2. Percentage Targets**
```yaml
fiscal_balance_percent_gdp:
  2025: -3.7  # -3.7% of GDP target
```

**3. Growth Rate Targets**
```yaml
gdp_real_growth_percent:
  2025: 5.0   # 5% real growth target
```

## 🔧 Adjustment Rules Configuration

### **File Structure** (`config/adjustment_rules.yaml`)
```yaml
# Optimization Strategy
optimization:
  method: "hybrid"           # hybrid, sequential, simultaneous
  max_iterations: 100
  convergence_tolerance: 1e-6
  crisis_economy_mode: true

# Constraint Handling
constraints:
  preserve_2022_fiscal_revenue: true
  maintain_oil_exports: true
  crisis_tolerance_percent: 15.0
  max_statistical_discrepancy_pct_gdp: 2.0

# Variable Priorities
priorities:
  gdp_targets: 1.0          # Highest priority
  fiscal_targets: 0.8
  trade_targets: 0.9
  inflation_targets: 1.0
  current_account_targets: 0.6

# Crisis Economy Settings
crisis_economy:
  trade_consistency_tolerance: 0.15    # 15%
  bop_gap_tolerance: 0.20             # 20%
  statistical_discrepancy_limit: 0.02  # 2% of GDP
  allow_errors_omissions_adjustment: true
```

## 🌍 Country-Specific Adaptations

### **Yemen Configuration (Current)**
```yaml
country: "Yemen"
crisis_economy: true
conflict_affected: true
data_quality: "limited"

# Yemen-specific tolerances
tolerances:
  trade_consistency: 0.15      # 15% for crisis economy
  bop_financing_gap: 0.20      # 20% for conflict zones
  fiscal_data_gaps: 0.10       # 10% for revenue estimates
```

### **Adapting for Other Countries**

**Stable Economy Example**:
```yaml
country: "Jordan"
crisis_economy: false
conflict_affected: false
data_quality: "high"

tolerances:
  trade_consistency: 0.05      # 5% for stable economies
  bop_financing_gap: 0.05      # 5% for normal conditions
  fiscal_data_gaps: 0.02       # 2% for reliable data
```

**Post-Conflict Economy Example**:
```yaml
country: "Iraq"
crisis_economy: true
conflict_affected: true
data_quality: "improving"

tolerances:
  trade_consistency: 0.12      # 12% for post-conflict
  bop_financing_gap: 0.15      # 15% for reconstruction
  fiscal_data_gaps: 0.08       # 8% for rebuilding institutions
```

## 📊 Data Configuration

### **Required Data Structure**
The system expects CSV data with specific variable codes:

**GDP Variables**:
- `YEMNYGDPMKTPCN` - GDP Nominal (LCU)
- `YEMNYGDPMKTPKN` - GDP Real (LCU)
- `YEMNYGDPMKTPCD` - GDP Deflator

**Fiscal Variables**:
- `YEMGGREVTOTLCN` - Total Revenue (LCU)
- `YEMGGEXPTOTLCN` - Total Expenditure (LCU)
- `YEMGGBALOVRLCN` - Fiscal Balance (LCU)

**External Variables**:
- `YEMNEEXPGNFSCN` - Exports (LCU)
- `YEMNEIMPGNFSCN` - Imports (LCU)
- `YEMPANUSATLS` - Exchange Rate (LCU/USD)

### **Data Quality Requirements**
```yaml
data_quality:
  minimum_years: 10          # At least 10 years of data
  required_variables: 45     # Core macroeconomic variables
  completeness_threshold: 0.8 # 80% data completeness
  consistency_checks: true   # Automatic validation
```

## 🎛️ Advanced Configuration

### **Optimization Parameters**
```yaml
optimization_advanced:
  # Goal-seek settings
  goal_seek:
    max_iterations: 50
    tolerance: 1e-8
    step_size: 0.01
    
  # Multi-stage settings
  stages:
    gdp_alignment:
      priority: 1.0
      tolerance: 0.001
    fiscal_alignment:
      priority: 0.8
      tolerance: 0.005
    trade_alignment:
      priority: 0.9
      tolerance: 0.01
```

### **Identity Validation Settings**
```yaml
identity_validation:
  # Tolerance levels (as percentages)
  gdp_expenditure: 0.001      # 0.001% tolerance
  gdp_production: 0.001       # 0.001% tolerance
  fiscal_identities: 0.1      # 0.1% tolerance
  bop_identity: 0.0           # Perfect balance required
  savings_investment: 0.001   # 0.001% tolerance
  trade_consistency: 0.001    # 0.001% for MFMOD compliance
```

### **MFMOD Compliance Settings**
```yaml
mfmod_compliance:
  strict_enforcement: true
  tolerance_ratio: 0.001      # 0.1% tolerance (0.999-1.001)
  preserve_goods_services_split: true
  automatic_bop_adjustment: true
  logging_level: "detailed"
```

## 🔄 Scenario Configuration

### **Conservative Scenario**
```yaml
scenario: "conservative"
target_achievement_range: [0.95, 1.05]  # 95-105%
risk_tolerance: "low"
identity_preservation: "strict"
```

### **Ambitious Scenario**
```yaml
scenario: "ambitious"
target_achievement_range: [0.98, 1.15]  # 98-115%
risk_tolerance: "medium"
identity_preservation: "flexible"
```

### **Crisis Response Scenario**
```yaml
scenario: "crisis_response"
target_achievement_range: [0.85, 1.20]  # 85-120%
risk_tolerance: "high"
identity_preservation: "adaptive"
crisis_tolerances: "maximum"
```

## 🚀 Quick Configuration Examples

### **Standard IMF Program**
```bash
# Use default configuration
python3 align_to_imf.py
```

### **Custom Target Set**
```bash
# Modify config/imf_targets.yaml first, then:
python3 align_to_imf.py --config custom_targets.yaml
```

### **Different Country**
```bash
# Prepare country-specific data and configuration
python3 align_to_imf.py --country jordan --data jordan_data.csv
```

## ✅ Configuration Validation

### **Pre-Run Checklist**
- [ ] **IMF targets complete** for all required years
- [ ] **Data file accessible** and properly formatted
- [ ] **Variable codes consistent** with system expectations
- [ ] **Tolerance levels appropriate** for country context
- [ ] **Constraint settings realistic** for economic conditions

### **Common Configuration Issues**

**Issue**: Missing target years
```yaml
# ❌ Incomplete
gdp_nominal_usd_billions:
  2022: 21.6
  # Missing 2023, 2024, 2025

# ✅ Complete
gdp_nominal_usd_billions:
  2022: 21.6
  2023: 24.0
  2024: 29.4
  2025: 40.1
```

**Issue**: Inconsistent units
```yaml
# ❌ Mixed units
exports_total_usd_billions: 2.7    # Billions
imports_usd_millions: 16700       # Millions

# ✅ Consistent units
exports_total_usd_billions: 2.7    # Billions
imports_usd_billions: 16.7        # Billions
```

## 🏆 Best Practices

### **Configuration Management**
1. **Version Control**: Track configuration changes
2. **Documentation**: Comment complex settings
3. **Validation**: Test configurations before production
4. **Backup**: Maintain working configurations

### **Country Adaptation**
1. **Research Context**: Understand economic conditions
2. **Calibrate Tolerances**: Set appropriate levels
3. **Validate Results**: Check economic reasonableness
4. **Iterate**: Refine based on outcomes

### **Quality Assurance**
1. **Peer Review**: Have configurations reviewed
2. **Testing**: Run with known datasets
3. **Monitoring**: Track performance metrics
4. **Documentation**: Maintain change logs

## 📞 Configuration Support

For configuration assistance:
1. **Review Examples**: Check existing configurations
2. **Consult Documentation**: Read technical guides
3. **Test Incrementally**: Start with small changes
4. **Validate Results**: Ensure economic logic

---

*Next: [Troubleshooting Guide](troubleshooting.md) | [Technical Documentation](../technical/)*
