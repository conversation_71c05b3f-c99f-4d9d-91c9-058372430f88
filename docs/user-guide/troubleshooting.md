# 🔧 Troubleshooting Guide

This guide helps you diagnose and resolve common issues with the **Hybrid Optimization Macroeconomic Framework**.

## 🚨 Common Issues & Solutions

### **1. Import/Installation Issues**

**Error**: `ModuleNotFoundError: No module named 'pandas'`
```bash
# Solution: Install required packages
pip install pandas numpy scipy pyyaml
```

**Error**: `ImportError: cannot import name 'YemenDataHandler'`
```bash
# Solution: Check Python path and file structure
export PYTHONPATH="${PYTHONPATH}:/path/to/macroeconomic-framework"
```

**Error**: `FileNotFoundError: [Errno 2] No such file or directory: 'data/yemen_macro_data.csv'`
```bash
# Solution: Ensure you're in the correct directory
cd /path/to/macroeconomic-framework
python3 align_to_imf.py
```

### **2. Data Loading Issues**

**Error**: `Exchange rate for 2025 is zero or missing`
```yaml
# Solution: Check exchange rate data in CSV
# Ensure YEMPANUSATLS variable has values for all target years
```

**Error**: `No original values stored for 2023, skipping BOP synchronization`
```bash
# This is a WARNING, not an error - system continues normally
# The optimization process handles this automatically
```

**Error**: `KeyError: 'Unnamed: 67'`
```python
# Solution: Check CSV column structure
# Ensure year columns are properly named or mapped
```

### **3. Optimization Issues**

**Error**: `Goal seek for YEMNYGDPMKTPCN failed to converge`
```yaml
# Solution: Check target reasonableness
gdp_nominal_usd_billions:
  2025: 40.1  # Ensure this is realistic given base year
```

**Error**: `Identity validation failed after adjustment`
```bash
# This triggers automatic rollback - system is working correctly
# Check if targets are mathematically consistent
```

**Error**: `Hybrid optimization failed: '2022'`
```bash
# System automatically falls back to proven method
# This is expected behavior - no action needed
```

### **4. MFMOD Compliance Issues**

**Warning**: `Export consistency check: MFMOD will flag this!`
```bash
# Solution: This is resolved automatically by trade consistency enforcer
# Look for: "🔧 MFMOD compliance enforced" in logs
```

**Error**: `Cannot synchronize BOP for 2024: exchange rate is zero`
```csv
# Solution: Check exchange rate data
YEMPANUSATLS,2024,1824.5  # Ensure non-zero values
```

### **5. Configuration Issues**

**Error**: `yaml.scanner.ScannerError: mapping values are not allowed here`
```yaml
# Solution: Fix YAML syntax
# ❌ Incorrect
gdp_targets
  2022: 21.6

# ✅ Correct  
gdp_targets:
  2022: 21.6
```

**Error**: `Target year 2026 not found in configuration`
```python
# Solution: Ensure target years match configuration
# Check config/imf_targets.yaml for required years
```

## 🔍 Diagnostic Steps

### **Step 1: Basic System Check**
```bash
# Check Python version
python3 --version  # Should be 3.8+

# Check required packages
python3 -c "import pandas, numpy, scipy, yaml; print('All packages available')"

# Check file structure
ls -la data/yemen_macro_data.csv
ls -la config/imf_targets.yaml
```

### **Step 2: Data Validation**
```python
# Quick data check
import pandas as pd
data = pd.read_csv('data/yemen_macro_data.csv')
print(f"Data shape: {data.shape}")
print(f"Years available: {data.columns[1:].tolist()}")

# Check for key variables
key_vars = ['YEMNYGDPMKTPCN', 'YEMPANUSATLS', 'YEMNEEXPGNFSCN']
for var in key_vars:
    if var in data['Variable'].values:
        print(f"✅ {var} found")
    else:
        print(f"❌ {var} missing")
```

### **Step 3: Configuration Validation**
```python
# Check IMF targets
import yaml
with open('config/imf_targets.yaml', 'r') as f:
    targets = yaml.safe_load(f)
    
print("Available target categories:")
for category in targets.keys():
    print(f"  - {category}")
    
print("GDP targets:")
for year, value in targets['real_sector']['gdp_nominal_usd_billions'].items():
    print(f"  {year}: ${value}B")
```

## 📊 Performance Issues

### **Slow Execution**
```bash
# Check data size
wc -l data/yemen_macro_data.csv

# Monitor memory usage
python3 -c "
import psutil
import os
process = psutil.Process(os.getpid())
print(f'Memory usage: {process.memory_info().rss / 1024 / 1024:.1f} MB')
"
```

**Solutions**:
- Reduce data years if not needed
- Increase system memory
- Close other applications

### **Convergence Issues**
```bash
# Check for unrealistic targets
# GDP growth > 50% per year may cause convergence issues
# Fiscal balance swings > 10% GDP may be problematic
```

**Solutions**:
- Review target reasonableness
- Check base year data quality
- Adjust convergence tolerance

## 🔧 Advanced Troubleshooting

### **Debug Mode**
```python
# Enable detailed logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Run with verbose output
python3 align_to_imf.py --verbose
```

### **Identity Validation Debugging**
```python
# Check specific identity
from src.identity_validator import IdentityValidator
from src.data_handler import YemenDataHandler

data_handler = YemenDataHandler('data/yemen_macro_data.csv')
validator = IdentityValidator(data_handler)

# Test specific identity
result = validator.validate_gdp_expenditure_identity([2022, 2023, 2024, 2025])
print(f"GDP expenditure identity: {result}")
```

### **Trade Consistency Debugging**
```python
# Check trade consistency manually
na_exports = data_handler.get_variable('YEMNEEXPGNFSCN', [2025]).get(2025, 0)
exchange_rate = data_handler.get_variable('YEMPANUSATLS', [2025]).get(2025, 0)
bop_exports_goods = data_handler.get_variable('YEMBXGSRMRCHCD', [2025]).get(2025, 0)
bop_exports_services = data_handler.get_variable('YEMBXGSRNFSVCD', [2025]).get(2025, 0)

expected_bop_total = na_exports / exchange_rate
actual_bop_total = bop_exports_goods + bop_exports_services
ratio = (actual_bop_total * exchange_rate) / na_exports

print(f"NA Exports: {na_exports:,.0f} LCU")
print(f"Exchange Rate: {exchange_rate:.0f}")
print(f"Expected BOP: {expected_bop_total:.1f} USD")
print(f"Actual BOP: {actual_bop_total:.1f} USD")
print(f"Consistency Ratio: {ratio:.6f}")
```

## 📋 Error Code Reference

### **System Errors**
- **E001**: Data file not found → Check file path
- **E002**: Configuration file invalid → Validate YAML syntax
- **E003**: Required variable missing → Check data completeness
- **E004**: Exchange rate zero/missing → Verify exchange rate data

### **Optimization Errors**
- **O001**: Goal seek convergence failure → Check target reasonableness
- **O002**: Identity validation failure → Review mathematical consistency
- **O003**: Hybrid optimization error → System falls back automatically
- **O004**: Manual adjustment failure → Targets may be incompatible

### **Validation Warnings**
- **W001**: Trade consistency gap → Handled by MFMOD enforcer
- **W002**: Statistical discrepancy high → Within crisis tolerance
- **W003**: BOP synchronization skipped → Normal for some iterations
- **W004**: Crisis tolerance applied → Expected for Yemen

## 🆘 Emergency Procedures

### **Complete System Reset**
```bash
# 1. Backup current configuration
cp config/imf_targets.yaml config/imf_targets_backup.yaml

# 2. Reset to default configuration
git checkout config/imf_targets.yaml

# 3. Clear any cached data
rm -rf __pycache__/
rm -rf src/__pycache__/

# 4. Restart with clean state
python3 align_to_imf.py
```

### **Data Recovery**
```bash
# If data file corrupted
git checkout data/yemen_macro_data.csv

# If outputs corrupted
rm -rf outputs/
mkdir outputs/
```

### **Configuration Recovery**
```bash
# Restore working configuration
cp config/imf_targets_backup.yaml config/imf_targets.yaml

# Or use minimal configuration
cat > config/imf_targets_minimal.yaml << EOF
real_sector:
  gdp_nominal_usd_billions:
    2022: 21.6
    2023: 24.0
    2024: 29.4
    2025: 40.1
EOF
```

## 📞 Getting Help

### **Self-Help Resources**
1. **Check Logs**: Review console output for specific errors
2. **Validate Data**: Ensure data file integrity
3. **Test Configuration**: Use minimal configuration first
4. **Review Documentation**: Check technical guides

### **Escalation Path**
1. **Basic Issues**: Review this troubleshooting guide
2. **Configuration Issues**: Check [Configuration Guide](configuration.md)
3. **Technical Issues**: Review [Technical Documentation](../technical/)
4. **Advanced Issues**: Examine [Implementation Details](../implementation/)

### **Information to Provide**
When seeking help, include:
- **Error message** (complete text)
- **System information** (OS, Python version)
- **Configuration files** (imf_targets.yaml)
- **Data file status** (size, format)
- **Console output** (last 50 lines)

## ✅ Prevention Best Practices

### **Regular Maintenance**
- **Backup configurations** before changes
- **Validate data** before processing
- **Test changes** incrementally
- **Monitor performance** metrics

### **Quality Assurance**
- **Peer review** configurations
- **Document changes** thoroughly
- **Test edge cases** regularly
- **Maintain version control**

### **System Health**
- **Monitor disk space** (outputs can be large)
- **Check memory usage** during execution
- **Validate results** economically
- **Update dependencies** periodically

---

*Need more help? Check [Getting Started Guide](getting-started.md) | [Technical Documentation](../technical/)*
