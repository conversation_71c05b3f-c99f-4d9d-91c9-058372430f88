#!/usr/bin/env python3
"""
IMF-WB Data Comparison: Comprehensive comparison of IMF WEO and World Bank Framework data for Yemen (2022-2025)
"""

import pandas as pd
import numpy as np

def create_final_table():
    """Create final comparison table with manual data extraction"""
    
    # Manual extraction of WEO data for 2022-2025 from the CSV file
    weo_data = {
        # GDP data (Billions LCU)
        'gdp_nominal': {2022: 26240.359, 2023: 26305.900, 2024: 34731.233, 2025: 41202.299},
        'gdp_real': {2022: 213.643, 2023: 209.370, 2024: 206.230, 2025: 203.136},
        'gdp_deflator': {2022: 12282.323, 2023: 12564.287, 2024: 16841.030, 2025: 20283.069},
        'gdp_per_capita': {2022: 686509.297, 2023: 667818.399, 2024: 855803.956, 2025: 986317.327},
        
        # Investment (% of GDP)
        'investment_pct': {2022: 5.418, 2023: 5.478, 2024: 5.399, 2025: 6.087},
        
        # Inflation
        'cpi_index': {2022: 2750.158, 2023: 2773.761, 2024: 3714.555, 2025: 4473.801},
        'inflation_rate': {2022: 29.511, 2023: 0.858, 2024: 33.918, 2025: 20.440},
        
        # Government Finance (Billions LCU)
        'gov_revenue': {2022: 2629.472, 2023: 1611.133, 2024: 2235.000, 2025: 2412.277},
        'gov_revenue_pct': {2022: 10.021, 2023: 6.125, 2024: 6.435, 2025: 5.855},
        'gov_expenditure': {2022: 3194.000, 2023: 3093.000, 2024: 3094.959, 2025: 3949.889},
        'gov_expenditure_pct': {2022: 12.172, 2023: 11.758, 2024: 8.911, 2025: 9.587},
        'gov_balance': {2022: -564.528, 2023: -1481.867, 2024: -859.959, 2025: -1537.612},
        'gov_balance_pct': {2022: -2.151, 2023: -5.633, 2024: -2.476, 2025: -3.732},
        
        # External sector (Billions USD)
        'current_account': {2022: -3.561, 2023: -2.360, 2024: -3.368, 2025: -2.102},
        'current_account_pct': {2022: -15.130, 2023: -12.157, 2024: -17.635, 2025: -12.082},
        
        # Demographics (Millions)
        'population': {2022: 38.223, 2023: 39.391, 2024: 40.583, 2025: 41.774}
    }
    
    # Load WB data
    raw_df = pd.read_csv('data/yemen_macro_data.csv')
    years_row = raw_df.iloc[0, 2:].values
    years = [int(y) for y in years_row if str(y).isdigit()]
    
    wb_data = {}
    for idx in range(1, len(raw_df)):
        indicator_code = raw_df.iloc[idx, 0] 
        if pd.isna(indicator_code) or indicator_code == '':
            continue
            
        values = raw_df.iloc[idx, 2:2+len(years)].values
        numeric_values = []
        for v in values:
            if pd.isna(v) or str(v).upper() == 'NAN':
                numeric_values.append(np.nan)
            else:
                try:
                    numeric_values.append(float(v))
                except:
                    numeric_values.append(np.nan)
        
        wb_data[indicator_code] = dict(zip(years, numeric_values))
    
    # Build comparison table
    years = [2022, 2023, 2024, 2025]
    table_data = []
    
    # Define comparisons
    comparisons = [
        {
            'indicator': 'GDP Nominal (Billions LCU)',
            'weo': weo_data['gdp_nominal'],
            'wb_key': 'YEMNYGDPMKTPCN',
            'wb_factor': 0.001
        },
        {
            'indicator': 'GDP Real (Billions LCU)', 
            'weo': weo_data['gdp_real'],
            'wb_key': 'YEMNYGDPMKTPKN',
            'wb_factor': 0.001
        },
        {
            'indicator': 'GDP Deflator (Index)',
            'weo': weo_data['gdp_deflator'],
            'wb_key': None,  # Calculate
            'wb_factor': 1
        },
        {
            'indicator': 'GDP per capita (LCU)',
            'weo': weo_data['gdp_per_capita'],
            'wb_key': None,  # Calculate
            'wb_factor': 1
        },
        {
            'indicator': 'Investment (% of GDP)',
            'weo': weo_data['investment_pct'],
            'wb_key': None,  # Calculate
            'wb_factor': 1
        },
        {
            'indicator': 'CPI Index (2000=100)',
            'weo': weo_data['cpi_index'],
            'wb_key': 'YEMFPCPITOTLXN',
            'wb_factor': 1
        },
        {
            'indicator': 'Inflation Rate (%)',
            'weo': weo_data['inflation_rate'],
            'wb_key': None,  # Calculate
            'wb_factor': 1
        },
        {
            'indicator': 'Gov Revenue (Billions LCU)',
            'weo': weo_data['gov_revenue'],
            'wb_key': 'YEMGGREVTOTLCN',
            'wb_factor': 0.001
        },
        {
            'indicator': 'Gov Revenue (% of GDP)',
            'weo': weo_data['gov_revenue_pct'],
            'wb_key': None,  # Calculate
            'wb_factor': 1
        },
        {
            'indicator': 'Gov Expenditure (Billions LCU)',
            'weo': weo_data['gov_expenditure'],
            'wb_key': 'YEMGGEXPTOTLCN',
            'wb_factor': 0.001
        },
        {
            'indicator': 'Gov Expenditure (% of GDP)',
            'weo': weo_data['gov_expenditure_pct'],
            'wb_key': None,  # Calculate
            'wb_factor': 1
        },
        {
            'indicator': 'Gov Balance (Billions LCU)',
            'weo': weo_data['gov_balance'],
            'wb_key': 'YEMGGBALOVRLCN',
            'wb_factor': 0.001
        },
        {
            'indicator': 'Gov Balance (% of GDP)',
            'weo': weo_data['gov_balance_pct'],
            'wb_key': None,  # Calculate
            'wb_factor': 1
        },
        {
            'indicator': 'Current Account (Billions USD)',
            'weo': weo_data['current_account'],
            'wb_key': 'YEMBNCABFUNDCD',
            'wb_factor': 0.001
        },
        {
            'indicator': 'Current Account (% of GDP)',
            'weo': weo_data['current_account_pct'],
            'wb_key': None,  # Calculate from USD/GDP ratio
            'wb_factor': 1
        },
        {
            'indicator': 'Population (Millions)',
            'weo': weo_data['population'],
            'wb_key': 'YEMSPPOPTOTL',
            'wb_factor': 1
        }
    ]
    
    for comp in comparisons:
        row = {'Indicator': comp['indicator']}
        
        # Get WB values
        wb_values = {}
        if comp['wb_key'] and comp['wb_key'] in wb_data:
            wb_raw = wb_data[comp['wb_key']]
            for year in years:
                if year in wb_raw and not pd.isna(wb_raw[year]):
                    wb_values[year] = wb_raw[year] * comp['wb_factor']
        
        # Calculate WB values for derived indicators
        elif comp['wb_key'] is None:
            if 'GDP Deflator' in comp['indicator']:
                wb_nom = wb_data.get('YEMNYGDPMKTPCN', {})
                wb_real = wb_data.get('YEMNYGDPMKTPKN', {})
                for year in years:
                    if year in wb_nom and year in wb_real and wb_real[year] != 0:
                        wb_values[year] = (wb_nom[year] / wb_real[year]) * 100
                        
            elif 'GDP per capita' in comp['indicator']:
                wb_gdp = wb_data.get('YEMNYGDPMKTPCN', {})
                wb_pop = wb_data.get('YEMSPPOPTOTL', {})
                for year in years:
                    if year in wb_gdp and year in wb_pop and wb_pop[year] != 0:
                        wb_values[year] = wb_gdp[year] / wb_pop[year]
                        
            elif 'Investment (% of GDP)' in comp['indicator']:
                wb_inv = wb_data.get('YEMNEGDIFTOTCN', {})
                wb_gdp = wb_data.get('YEMNYGDPMKTPCN', {})
                for year in years:
                    if year in wb_inv and year in wb_gdp and wb_gdp[year] != 0:
                        wb_values[year] = (wb_inv[year] / wb_gdp[year]) * 100
                        
            elif 'Inflation Rate' in comp['indicator']:
                wb_cpi = wb_data.get('YEMFPCPITOTLXN', {})
                for year in years:
                    if year in wb_cpi and (year-1) in wb_cpi and wb_cpi[year-1] != 0:
                        wb_values[year] = ((wb_cpi[year] / wb_cpi[year-1]) - 1) * 100
                        
            elif 'Gov Revenue (% of GDP)' in comp['indicator']:
                wb_rev = wb_data.get('YEMGGREVTOTLCN', {})
                wb_gdp = wb_data.get('YEMNYGDPMKTPCN', {})
                for year in years:
                    if year in wb_rev and year in wb_gdp and wb_gdp[year] != 0:
                        wb_values[year] = (wb_rev[year] / wb_gdp[year]) * 100
                        
            elif 'Gov Expenditure (% of GDP)' in comp['indicator']:
                wb_exp = wb_data.get('YEMGGEXPTOTLCN', {})
                wb_gdp = wb_data.get('YEMNYGDPMKTPCN', {})
                for year in years:
                    if year in wb_exp and year in wb_gdp and wb_gdp[year] != 0:
                        wb_values[year] = (wb_exp[year] / wb_gdp[year]) * 100
                        
            elif 'Gov Balance (% of GDP)' in comp['indicator']:
                wb_bal = wb_data.get('YEMGGBALOVRLCN', {})
                wb_gdp = wb_data.get('YEMNYGDPMKTPCN', {})
                for year in years:
                    if year in wb_bal and year in wb_gdp and wb_gdp[year] != 0:
                        wb_values[year] = (wb_bal[year] / wb_gdp[year]) * 100
        
        # Add year columns
        for year in years:
            weo_val = comp['weo'].get(year, np.nan)
            wb_val = wb_values.get(year, np.nan)
            
            def format_value(val):
                if pd.isna(val):
                    return "N/A"
                elif abs(val) >= 10000:
                    return f"{val:,.0f}"
                elif abs(val) >= 100:
                    return f"{val:.0f}"
                elif abs(val) >= 1:
                    return f"{val:.1f}"
                else:
                    return f"{val:.2f}"
            
            row[f'{year}_IMF'] = format_value(weo_val)
            row[f'{year}_WB'] = format_value(wb_val)
        
        table_data.append(row)
    
    return pd.DataFrame(table_data)

def main():
    """Generate final comparison table"""
    print("IMF WEO vs World Bank Framework Comparison (2022-2025)")
    print("=" * 140)
    
    df = create_final_table()
    
    # Print table
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', 35)
    
    print(df.to_string(index=False))
    
    print("\n" + "=" * 140)
    print("Data Sources:")
    print("- IMF: World Economic Outlook Database (March 2025)")
    print("- WB: World Bank Macroeconomic Framework Dataset")
    print("- All values converted to comparable units")
    
    # Save to CSV
    df.to_csv('data/imf_wb_yemen_comparison_2022_2025.csv', index=False)
    print(f"\nSaved to: data/imf_wb_yemen_comparison_2022_2025.csv")

if __name__ == "__main__":
    main()