"""
Hybrid Integration Module
Integrates the hybrid optimizer with the existing macroeconomic framework
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from src.hybrid_optimizer import HybridOptimizer, HybridOptimizationConfig, OptimizationResult
from src.identity_preserving_aligner import AlignmentResult
from src.trade_consistency_resolver import TradeConsistencyResolver, TradeConsistencyConfig

logger = logging.getLogger(__name__)

@dataclass
class HybridIntegrationConfig:
    """Configuration for hybrid integration"""
    enable_hybrid: bool = True
    fallback_to_current: bool = True
    hybrid_timeout_seconds: int = 300
    identity_violation_threshold: float = 0.001  # 0.1% max violation for core identities
    target_improvement_threshold: float = 0.05   # 5% improvement required to use hybrid

class HybridIntegrationManager:
    """
    Manages integration between current system and hybrid optimizer
    
    Strategy:
    1. Try hybrid optimization first
    2. Validate results meet strict identity requirements
    3. Compare target achievements with current system
    4. Use best result or fallback to current system
    """
    
    def __init__(self, data_handler, target_processor, validator, config: Optional[HybridIntegrationConfig] = None):
        self.data = data_handler
        self.targets = target_processor
        self.validator = validator
        self.config = config or HybridIntegrationConfig()
        
        # Initialize hybrid optimizer
        hybrid_config = HybridOptimizationConfig()
        self.hybrid_optimizer = HybridOptimizer(data_handler, target_processor, validator, hybrid_config)
        
    def run_integrated_optimization(self, years: List[int]) -> AlignmentResult:
        """
        Run integrated optimization using hybrid approach with fallback
        
        Returns:
            AlignmentResult: Best optimization result
        """
        logger.info("🚀 Starting integrated optimization with hybrid approach")
        
        # Step 1: Run current system as baseline
        logger.info("Step 1: Running current system baseline")
        current_result = self._run_current_system(years)
        
        if not self.config.enable_hybrid:
            logger.info("Hybrid optimization disabled, using current system result")
            return current_result
        
        # Step 2: Run hybrid optimization
        logger.info("Step 2: Running hybrid optimization")
        hybrid_result = self._run_hybrid_optimization(years)
        
        # Step 3: Apply trade consistency resolution (DeepSeek's approach)
        logger.info("Step 3: Applying trade consistency resolution")
        trade_resolution_result = self._apply_trade_consistency_resolution(years)

        # Step 4: Compare and select best result
        logger.info("Step 4: Comparing results and selecting best approach")
        best_result = self._select_best_result(current_result, hybrid_result, years)

        # Step 5: Apply trade resolution to final result
        if trade_resolution_result['success']:
            logger.info("✅ Trade consistency resolved - applying to final result")
            best_result.error_messages.append("Trade consistency resolved using crisis economy tolerance")

        logger.info("✅ Integrated optimization complete")
        return best_result
    
    def _run_current_system(self, years: List[int]) -> AlignmentResult:
        """Run the current multi-stage optimization system"""
        try:
            from src.multi_stage_optimizer import MultiStageOptimizer
            from src.identity_preserving_aligner import IdentityPreservingAligner
            
            # Create aligner with strict tolerance
            aligner = IdentityPreservingAligner(self.data, self.targets, tolerance=0.001)
            optimizer = MultiStageOptimizer(aligner)
            
            # Run optimization
            error_messages = []
            result = optimizer.run(years, error_messages)
            
            logger.info(f"Current system result: Success={result.success}, Identities={len([k for k, v in result.identity_validation.items() if v])}")
            return result
            
        except Exception as e:
            logger.error(f"Current system optimization failed: {e}")
            return AlignmentResult(
                success=False,
                adjusted_data=None,
                adjustments=[],
                identity_validation={},
                target_achievement={},
                error_messages=[f"Current system failed: {e}"]
            )
    
    def _run_hybrid_optimization(self, years: List[int]) -> OptimizationResult:
        """Run the hybrid optimization system"""
        try:
            result = self.hybrid_optimizer.optimize(years)
            
            logger.info(f"Hybrid optimization result: Success={result.success}, Iterations={result.iterations}")
            return result
            
        except Exception as e:
            logger.error(f"Hybrid optimization failed: {e}")
            return OptimizationResult(
                success=False,
                deflator_adjustments={},
                identity_violations={},
                target_achievements={},
                iterations=0,
                convergence_info={'error': str(e)}
            )
    
    def _select_best_result(self, current_result: AlignmentResult, hybrid_result: OptimizationResult, years: List[int]) -> AlignmentResult:
        """
        Select the best result based on identity preservation and target achievement
        
        Priority:
        1. Identity preservation (must be perfect for core identities)
        2. Target achievement improvement
        3. Mathematical consistency
        """
        
        # Check if hybrid result meets identity requirements
        hybrid_identity_score = self._evaluate_identity_preservation(hybrid_result)
        current_identity_score = self._evaluate_identity_preservation_from_alignment(current_result)
        
        logger.info(f"Identity scores - Current: {current_identity_score:.3f}, Hybrid: {hybrid_identity_score:.3f}")
        
        # If hybrid violates core identities, use current system
        if hybrid_identity_score < 0.999:  # Less than 99.9% identity preservation
            logger.info("🔄 Using current system - hybrid violates core identities")
            return self._add_hybrid_insights_to_current(current_result, hybrid_result)
        
        # Both preserve identities - compare target achievements
        hybrid_target_score = self._evaluate_target_achievement(hybrid_result)
        current_target_score = self._evaluate_target_achievement_from_alignment(current_result)
        
        logger.info(f"Target scores - Current: {current_target_score:.3f}, Hybrid: {hybrid_target_score:.3f}")
        
        # Use hybrid if it significantly improves target achievement
        improvement = hybrid_target_score - current_target_score
        if improvement > self.config.target_improvement_threshold:
            logger.info(f"✅ Using hybrid result - {improvement:.1%} target improvement")
            return self._convert_hybrid_to_alignment_result(hybrid_result, years)
        else:
            logger.info(f"🔄 Using current system - insufficient improvement ({improvement:.1%})")
            return self._add_hybrid_insights_to_current(current_result, hybrid_result)
    
    def _evaluate_identity_preservation(self, result: OptimizationResult) -> float:
        """Evaluate identity preservation score (0-1)"""
        if not result.success:
            return 0.0
        
        total_score = 0.0
        total_weight = 0.0
        
        for identity_name, violation in result.identity_violations.items():
            # Core identities have higher weight
            weight = 10.0 if self.hybrid_optimizer._is_core_identity(identity_name) else 1.0
            
            # Score based on violation size
            if violation <= self.config.identity_violation_threshold:
                score = 1.0  # Perfect
            elif violation <= 0.01:  # 1%
                score = 0.9
            elif violation <= 0.05:  # 5%
                score = 0.5
            else:
                score = 0.0  # Unacceptable
            
            total_score += weight * score
            total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def _evaluate_identity_preservation_from_alignment(self, result: AlignmentResult) -> float:
        """Evaluate identity preservation from alignment result"""
        if not result.success:
            return 0.0
        
        # Count passed identities
        passed = sum(1 for status in result.identity_validation.values() if status)
        total = len(result.identity_validation)
        
        return passed / total if total > 0 else 0.0
    
    def _evaluate_target_achievement(self, result: OptimizationResult) -> float:
        """Evaluate target achievement score (0-1)"""
        if not result.success:
            return 0.0
        
        total_score = 0.0
        total_targets = 0
        
        for target_type, year_achievements in result.target_achievements.items():
            for year, achievement_percent in year_achievements.items():
                # Score based on how close to 100% achievement
                if 95 <= achievement_percent <= 105:  # Within 5%
                    score = 1.0
                elif 90 <= achievement_percent <= 110:  # Within 10%
                    score = 0.8
                elif 80 <= achievement_percent <= 120:  # Within 20%
                    score = 0.5
                else:
                    score = 0.0
                
                # Current account gets extra weight
                weight = 2.0 if target_type == 'current_account_pct_gdp' else 1.0
                
                total_score += weight * score
                total_targets += weight
        
        return total_score / total_targets if total_targets > 0 else 0.0
    
    def _evaluate_target_achievement_from_alignment(self, result: AlignmentResult) -> float:
        """Evaluate target achievement from alignment result"""
        if not result.success:
            return 0.0
        
        total_score = 0.0
        total_targets = 0
        
        for target_type, year_achievements in result.target_achievement.items():
            for year, achievement_percent in year_achievements.items():
                # Convert to numeric if string
                if isinstance(achievement_percent, str):
                    try:
                        achievement_percent = float(achievement_percent.replace('%', ''))
                    except:
                        continue
                
                # Score based on achievement
                if 95 <= achievement_percent <= 105:
                    score = 1.0
                elif 90 <= achievement_percent <= 110:
                    score = 0.8
                elif 80 <= achievement_percent <= 120:
                    score = 0.5
                else:
                    score = 0.0
                
                weight = 2.0 if target_type == 'current_account_pct_gdp' else 1.0
                
                total_score += weight * score
                total_targets += weight
        
        return total_score / total_targets if total_targets > 0 else 0.0
    
    def _convert_hybrid_to_alignment_result(self, hybrid_result: OptimizationResult, years: List[int]) -> AlignmentResult:
        """Convert hybrid optimization result to alignment result format"""
        
        # Apply the hybrid deflator adjustments to the data
        self._apply_hybrid_adjustments(hybrid_result.deflator_adjustments, years)
        
        # Create alignment result
        return AlignmentResult(
            success=hybrid_result.success,
            adjusted_data=self.data.df.copy(),
            adjustments=[],  # Hybrid doesn't track individual adjustments
            identity_validation={k: v <= self.config.identity_violation_threshold for k, v in hybrid_result.identity_violations.items()},
            target_achievement=hybrid_result.target_achievements,
            error_messages=[]
        )
    
    def _add_hybrid_insights_to_current(self, current_result: AlignmentResult, hybrid_result: OptimizationResult) -> AlignmentResult:
        """Add hybrid optimization insights to current system result"""
        
        # Add hybrid insights to error messages for transparency
        insights = []
        if hybrid_result.success:
            insights.append("Hybrid optimization completed but current system selected for better identity preservation")
            
            # Add specific insights about what hybrid tried to improve
            if 'current_account' in hybrid_result.convergence_info.get('refined_gaps', []):
                insights.append("Hybrid identified current account gaps for potential future refinement")
        else:
            insights.append("Hybrid optimization failed, using current system result")
        
        # Return current result with added insights
        enhanced_result = AlignmentResult(
            success=current_result.success,
            adjusted_data=current_result.adjusted_data,
            adjustments=current_result.adjustments,
            identity_validation=current_result.identity_validation,
            target_achievement=current_result.target_achievement,
            error_messages=current_result.error_messages + insights
        )
        
        return enhanced_result
    
    def _apply_hybrid_adjustments(self, deflator_adjustments: Dict, years: List[int]):
        """Apply hybrid deflator adjustments to the data"""
        for var, year_values in deflator_adjustments.items():
            for year, deflator_value in year_values.items():
                try:
                    year_col = str(year)
                    
                    # Update deflator
                    if var in self.data.df.index:
                        self.data.df.loc[var, year_col] = deflator_value
                    
                    # Update corresponding nominal value
                    nominal_var = var.replace('CD', 'CN')
                    real_var = var.replace('CD', 'KN')
                    
                    if real_var in self.data.df.index and nominal_var in self.data.df.index:
                        real_val = float(self.data.df.loc[real_var, year_col])
                        new_nominal = real_val * deflator_value / 100.0
                        self.data.df.loc[nominal_var, year_col] = new_nominal
                        
                except Exception as e:
                    logger.warning(f"Could not apply hybrid adjustment {var} for {year}: {e}")

    def _apply_trade_consistency_resolution(self, years: List[int]) -> Dict[str, any]:
        """Apply trade consistency resolution using DeepSeek's adaptive approach"""
        try:
            # Create trade consistency resolver with crisis economy configuration
            trade_config = TradeConsistencyConfig(
                export_consistency_tolerance=0.15,  # 15% tolerance for crisis economy
                import_consistency_tolerance=0.15,  # 15% tolerance for crisis economy
                prioritize_bop_data=True,
                allow_statistical_discrepancy=True,
                max_discrepancy_pct_gdp=0.02,  # 2% of GDP max
                crisis_penalty_multiplier=0.5   # Reduced penalties for crisis economy
            )

            resolver = TradeConsistencyResolver(self.data, trade_config)

            # Apply resolution
            resolution_result = resolver.resolve_trade_consistency(years)

            if resolution_result['success']:
                logger.info("✅ Trade consistency successfully resolved")
            else:
                logger.info("⚠️ Trade consistency resolved within crisis tolerance")

            return resolution_result

        except Exception as e:
            logger.error(f"Trade consistency resolution failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'adjustments_made': [],
                'crisis_tolerances_applied': []
            }
