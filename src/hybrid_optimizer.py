"""
Hybrid Optimization Framework
Combines current system's identity preservation with DeepSeek's target achievement methods
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import scipy.optimize as opt
from copy import deepcopy

logger = logging.getLogger(__name__)

@dataclass
class HybridOptimizationConfig:
    """Configuration for hybrid optimization approach"""
    # Identity preservation (strict)
    identity_penalty_weight: float = 1e6  # Very high for core identities
    identity_tolerance: float = 0.001     # 0.1% for core identities
    
    # Crisis economy handling (adaptive)
    crisis_tolerance: float = 0.05        # 5% for crisis gaps
    adaptive_l1_weight: float = 100.0     # Base L1 penalty weight
    
    # Target achievement (enhanced)
    target_weight_base: float = 1.0       # Base target weight
    current_account_boost: float = 2.0    # Extra weight for current account
    
    # Penalty parameter adaptation
    penalty_param_initial: float = 10.0
    penalty_param_max: float = 1e6
    penalty_adaptation_factor: float = 2.0
    
    # Convergence criteria
    max_iterations: int = 50
    convergence_tolerance: float = 1e-6

@dataclass
class OptimizationResult:
    """Results from hybrid optimization"""
    success: bool
    deflator_adjustments: Dict[str, Dict[int, float]]
    identity_violations: Dict[str, float]
    target_achievements: Dict[str, float]
    iterations: int
    convergence_info: Dict[str, Any]

class HybridOptimizer:
    """
    Hybrid optimization framework combining:
    1. Current system's perfect identity preservation
    2. DeepSeek's adaptive penalty methods for target achievement
    3. Enhanced crisis economy handling
    """
    
    def __init__(self, data_handler, target_processor, validator, config: Optional[HybridOptimizationConfig] = None):
        self.data = data_handler
        self.targets = target_processor
        self.validator = validator
        self.config = config or HybridOptimizationConfig()
        
        # Optimization state
        self.penalty_param = self.config.penalty_param_initial
        self.lagrange_multipliers = {}
        self.optimization_history = []
        
    def optimize(self, years: List[int]) -> OptimizationResult:
        """
        Main hybrid optimization method
        
        Strategy:
        1. Start with current system's approach (perfect identities)
        2. Apply DeepSeek's adaptive methods for target improvement
        3. Use crisis tolerance only for non-core identities
        """
        logger.info("🚀 Starting hybrid optimization framework")
        
        try:
            # Phase 1: Ensure perfect identity preservation (current system)
            logger.info("Phase 1: Establishing identity preservation baseline")
            baseline_result = self._establish_identity_baseline(years)
            
            if not baseline_result.success:
                logger.error("Failed to establish identity baseline")
                return baseline_result
            
            # Phase 2: Apply DeepSeek's adaptive target optimization
            logger.info("Phase 2: Applying adaptive target optimization")
            enhanced_result = self._apply_adaptive_target_optimization(
                baseline_result.deflator_adjustments, years
            )
            
            # Phase 3: Crisis economy gap handling with adaptive penalties
            logger.info("Phase 3: Refining crisis economy gaps")
            final_result = self._refine_crisis_gaps(
                enhanced_result.deflator_adjustments, years
            )
            
            logger.info("✅ Hybrid optimization complete")
            return final_result
            
        except Exception as e:
            logger.error(f"Hybrid optimization failed: {e}")
            return OptimizationResult(
                success=False,
                deflator_adjustments={},
                identity_violations={},
                target_achievements={},
                iterations=0,
                convergence_info={'error': str(e)}
            )
    
    def _establish_identity_baseline(self, years: List[int]) -> OptimizationResult:
        """
        Phase 1: Use current system's approach to ensure perfect identity preservation
        This is our mathematical foundation - never compromise core identities
        """
        logger.info("Establishing identity preservation baseline using current system")
        
        # Use existing multi-stage optimizer for perfect identity preservation
        from src.multi_stage_optimizer import MultiStageOptimizer
        from src.identity_preserving_aligner import IdentityPreservingAligner
        
        # Create temporary aligner for baseline
        temp_aligner = IdentityPreservingAligner(self.data, self.targets, tolerance=0.001)  # Strict tolerance
        baseline_optimizer = MultiStageOptimizer(temp_aligner)
        
        # Run baseline optimization
        error_messages = []
        baseline_result = baseline_optimizer.run(years, error_messages)
        
        if baseline_result.success:
            logger.info("✅ Identity baseline established - all identities preserved")
            
            # Extract current deflator values as baseline
            baseline_deflators = self._extract_current_deflators(years)
            
            return OptimizationResult(
                success=True,
                deflator_adjustments=baseline_deflators,
                identity_violations={},
                target_achievements=baseline_result.target_achievement,
                iterations=1,
                convergence_info={'phase': 'baseline', 'identities_preserved': True}
            )
        else:
            logger.error("❌ Failed to establish identity baseline")
            return OptimizationResult(
                success=False,
                deflator_adjustments={},
                identity_violations={},
                target_achievements={},
                iterations=0,
                convergence_info={'error': 'baseline_failed', 'messages': error_messages}
            )
    
    def _apply_adaptive_target_optimization(self, baseline_deflators: Dict, years: List[int]) -> OptimizationResult:
        """
        Phase 2: Apply DeepSeek's adaptive methods for better target achievement
        while maintaining identity constraints with high penalty weights
        """
        logger.info("Applying adaptive target optimization with identity constraints")
        
        # Convert deflators to optimization vector
        x0 = self._deflators_to_vector(baseline_deflators, years)
        
        # Set up adaptive objective function
        def adaptive_objective(x):
            return self._calculate_adaptive_objective(x, years, baseline_deflators)
        
        # Set up strict identity constraints
        constraints = self._setup_strict_identity_constraints(years)
        
        # Set up bounds
        bounds = [(0.2, 2.0)] * len(x0)  # 20% to 200% deflator bounds
        
        # Run optimization with adaptive penalties
        result = opt.minimize(
            adaptive_objective,
            x0,
            method='SLSQP',
            bounds=bounds,
            constraints=constraints,
            options={
                'maxiter': self.config.max_iterations,
                'ftol': self.config.convergence_tolerance
            }
        )
        
        if result.success:
            logger.info("✅ Adaptive target optimization successful")
            optimized_deflators = self._vector_to_deflators(result.x, years, baseline_deflators)
            
            # Validate identities are still preserved
            identity_check = self._validate_identities(optimized_deflators, years)
            
            return OptimizationResult(
                success=True,
                deflator_adjustments=optimized_deflators,
                identity_violations=identity_check,
                target_achievements=self._calculate_target_achievements(optimized_deflators, years),
                iterations=result.nit,
                convergence_info={'phase': 'adaptive', 'scipy_result': result}
            )
        else:
            logger.warning("⚠️ Adaptive optimization failed, using baseline")
            return OptimizationResult(
                success=True,  # Still successful with baseline
                deflator_adjustments=baseline_deflators,
                identity_violations={},
                target_achievements=self._calculate_target_achievements(baseline_deflators, years),
                iterations=result.nit,
                convergence_info={'phase': 'adaptive_failed', 'using_baseline': True}
            )
    
    def _calculate_adaptive_objective(self, x: np.ndarray, years: List[int], baseline_deflators: Dict) -> float:
        """
        Calculate adaptive objective function combining:
        1. Target deviations (with current account boost)
        2. Identity preservation penalties (very high weights)
        3. Crisis gap penalties (adaptive L1)
        """
        # Convert vector to deflators
        deflators = self._vector_to_deflators(x, years, baseline_deflators)
        
        # Apply deflators temporarily
        original_data = self._backup_data()
        self._apply_deflators_temporarily(deflators, years)
        
        try:
            # 1. Target deviations with adaptive weights
            target_penalty = 0.0
            
            # Enhanced current account handling (DeepSeek's insight)
            current_account_targets = self._get_current_account_targets(years)
            for year, target in current_account_targets.items():
                achieved = self._calculate_current_account_achievement(year)
                deviation = abs(target - achieved) / abs(target) if target != 0 else abs(achieved)
                target_penalty += self.config.current_account_boost * deviation**2
            
            # Other targets with base weights
            other_targets = self._get_other_targets(years)
            for target_name, year_targets in other_targets.items():
                for year, target in year_targets.items():
                    achieved = self._calculate_target_achievement(target_name, year)
                    deviation = abs(target - achieved) / abs(target) if target != 0 else abs(achieved)
                    target_penalty += self.config.target_weight_base * deviation**2
            
            # 2. Identity preservation penalties (very high weights)
            identity_penalty = 0.0
            identity_violations = self._calculate_identity_violations(deflators, years)
            
            for identity_name, violation in identity_violations.items():
                if self._is_core_identity(identity_name):
                    # Core identities: extremely high penalty
                    identity_penalty += self.config.identity_penalty_weight * violation**2
                else:
                    # Non-core identities: crisis tolerance
                    if abs(violation) > self.config.crisis_tolerance:
                        identity_penalty += self.config.identity_penalty_weight * 0.1 * violation**2
            
            # 3. Adaptive L1 penalties for crisis gaps
            crisis_penalty = self._calculate_adaptive_crisis_penalty(deflators, years)
            
            total_objective = target_penalty + identity_penalty + crisis_penalty
            
            return total_objective
            
        finally:
            # Restore original data
            self.data.df = original_data
    
    def _is_core_identity(self, identity_name: str) -> bool:
        """Determine if an identity is core (never allow violations) or crisis-tolerant"""
        core_identities = {
            'gdp_expenditure_nominal',
            'gdp_expenditure_real', 
            'gdp_production_nominal',
            'gdp_production_real',
            'deflator_relationships',
            'investment_decomposition',
            'consumption_decomposition'
        }
        return identity_name in core_identities
    
    def _extract_current_deflators(self, years: List[int]) -> Dict[str, Dict[int, float]]:
        """Extract current deflator values from the data"""
        deflators = {}
        
        # Key deflator variables
        deflator_vars = [
            'YEMNYGDPMKTPCD',    # GDP deflator
            'YEMNECONPRVTCD',    # Private consumption deflator
            'YEMNECONGOVTCD',    # Government consumption deflator
            'YEMNEGDIFTOTCD',    # Investment deflator
            'YEMNEEXPGNFSCD',    # Export deflator
            'YEMNEIMPGNFSCD',    # Import deflator
            'YEMNVAGRTOTLCD',    # Agriculture deflator
            'YEMNVINDTOTLCD',    # Industry deflator
            'YEMNVSRVTOTLCD'     # Services deflator
        ]
        
        for var in deflator_vars:
            deflators[var] = {}
            for year in years:
                try:
                    # Calculate deflator from nominal/real ratio
                    nominal_var = var.replace('CD', 'CN')
                    real_var = var.replace('CD', 'KN')
                    
                    nominal = self.data.get_variable(nominal_var)
                    real = self.data.get_variable(real_var)
                    
                    year_col = str(year)
                    if year_col in nominal.index and year_col in real.index:
                        nom_val = float(nominal[year_col])
                        real_val = float(real[year_col])
                        
                        if real_val > 0:
                            deflators[var][year] = (nom_val / real_val) * 100
                        else:
                            deflators[var][year] = 100.0  # Default deflator
                    else:
                        deflators[var][year] = 100.0
                        
                except Exception as e:
                    logger.warning(f"Could not extract deflator for {var} in {year}: {e}")
                    deflators[var][year] = 100.0
        
        return deflators

    def _deflators_to_vector(self, deflators: Dict, years: List[int]) -> np.ndarray:
        """Convert deflator dictionary to optimization vector"""
        vector = []
        for var in sorted(deflators.keys()):
            for year in years:
                vector.append(deflators[var].get(year, 100.0) / 100.0)  # Normalize to 1.0 base
        return np.array(vector)

    def _vector_to_deflators(self, x: np.ndarray, years: List[int], template: Dict) -> Dict:
        """Convert optimization vector back to deflator dictionary"""
        deflators = {}
        idx = 0
        for var in sorted(template.keys()):
            deflators[var] = {}
            for year in years:
                deflators[var][year] = x[idx] * 100.0  # Convert back to deflator scale
                idx += 1
        return deflators

    def _backup_data(self):
        """Backup current data state"""
        return deepcopy(self.data.df)

    def _apply_deflators_temporarily(self, deflators: Dict, years: List[int]):
        """Apply deflators temporarily for objective function evaluation"""
        for var, year_values in deflators.items():
            for year, deflator_value in year_values.items():
                try:
                    # Update deflator column
                    year_col = str(year)
                    if var in self.data.df.index:
                        self.data.df.loc[var, year_col] = deflator_value

                    # Update corresponding nominal value using Nominal = Real × Deflator/100
                    nominal_var = var.replace('CD', 'CN')
                    real_var = var.replace('CD', 'KN')

                    if real_var in self.data.df.index and nominal_var in self.data.df.index:
                        real_val = float(self.data.df.loc[real_var, year_col])
                        new_nominal = real_val * deflator_value / 100.0
                        self.data.df.loc[nominal_var, year_col] = new_nominal

                except Exception as e:
                    logger.warning(f"Could not apply deflator {var} for {year}: {e}")

    def _setup_strict_identity_constraints(self, years: List[int]) -> List[Dict]:
        """Set up strict constraints for core economic identities"""
        constraints = []

        def gdp_expenditure_constraint(x):
            """GDP expenditure identity constraint"""
            deflators = self._vector_to_deflators(x, years, self._extract_current_deflators(years))
            original_data = self._backup_data()
            self._apply_deflators_temporarily(deflators, years)

            try:
                violations = []
                for year in years:
                    year_col = str(year)

                    # Get GDP components (nominal)
                    gdp = float(self.data.df.loc['YEMNYGDPMKTPCN', year_col])
                    consumption_prv = float(self.data.df.loc['YEMNECONPRVTCN', year_col])
                    consumption_gov = float(self.data.df.loc['YEMNECONGOVTCN', year_col])
                    investment = float(self.data.df.loc['YEMNEGDIFTOTCN', year_col])
                    inventory = float(self.data.df.loc['YEMNEGDISTKBCN', year_col])
                    exports = float(self.data.df.loc['YEMNEEXPGNFSCN', year_col])
                    imports = float(self.data.df.loc['YEMNEIMPGNFSCN', year_col])
                    stat_disc = float(self.data.df.loc['YEMNYGDPDISCCN', year_col])

                    calculated = consumption_prv + consumption_gov + investment + inventory + exports - imports + stat_disc
                    violation = gdp - calculated
                    violations.append(violation)

                return np.array(violations)
            finally:
                self.data.df = original_data

        constraints.append({
            'type': 'eq',
            'fun': gdp_expenditure_constraint
        })

        return constraints

    def _calculate_identity_violations(self, deflators: Dict, years: List[int]) -> Dict[str, float]:
        """Calculate violations for all economic identities"""
        violations = {}

        # Apply deflators temporarily
        original_data = self._backup_data()
        self._apply_deflators_temporarily(deflators, years)

        try:
            # Use existing validator
            validation_results = self.validator.validate_all(years, critical_only=False)

            for identity_name, result in validation_results.items():
                if isinstance(result, dict) and 'violation' in result:
                    violations[identity_name] = result['violation']
                elif isinstance(result, bool):
                    violations[identity_name] = 0.0 if result else 1.0  # Binary violation
                else:
                    violations[identity_name] = 0.0

        except Exception as e:
            logger.warning(f"Error calculating identity violations: {e}")

        finally:
            self.data.df = original_data

        return violations

    def _calculate_adaptive_crisis_penalty(self, deflators: Dict, years: List[int]) -> float:
        """Calculate adaptive L1 penalties for crisis economy gaps"""
        penalty = 0.0

        # Crisis-specific gaps that can be tolerated
        crisis_gaps = ['current_account_gap', 'savings_investment_gap', 'bop_gap']

        for gap_type in crisis_gaps:
            gap_values = self._calculate_crisis_gap(gap_type, deflators, years)

            for year, gap_value in gap_values.items():
                # Adaptive L1 weight based on gap severity
                if abs(gap_value) > self.config.crisis_tolerance:
                    # Increase penalty weight for larger gaps
                    adaptive_weight = self.config.adaptive_l1_weight * (1 + abs(gap_value))
                    penalty += adaptive_weight * abs(gap_value)

        return penalty

    def _calculate_crisis_gap(self, gap_type: str, deflators: Dict, years: List[int]) -> Dict[int, float]:
        """Calculate specific crisis economy gap values"""
        gaps = {}

        # Apply deflators temporarily
        original_data = self._backup_data()
        self._apply_deflators_temporarily(deflators, years)

        try:
            for year in years:
                if gap_type == 'current_account_gap':
                    # Calculate current account vs. target
                    target = self._get_current_account_target(year)
                    achieved = self._calculate_current_account_achievement(year)
                    gaps[year] = (achieved - target) / target if target != 0 else achieved

                elif gap_type == 'savings_investment_gap':
                    # Calculate savings-investment gap
                    gaps[year] = self._calculate_savings_investment_gap(year)

                elif gap_type == 'bop_gap':
                    # Calculate balance of payments gap
                    gaps[year] = self._calculate_bop_gap(year)

        except Exception as e:
            logger.warning(f"Error calculating {gap_type}: {e}")

        finally:
            self.data.df = original_data

        return gaps

    def _get_current_account_targets(self, years: List[int]) -> Dict[int, float]:
        """Get current account targets for specified years"""
        targets = {}
        for year in years:
            try:
                target_info = self.targets.get_target('current_account_pct_gdp', year)
                if target_info:
                    targets[year] = target_info.get('value', 0.0)
                else:
                    targets[year] = 0.0
            except:
                targets[year] = 0.0
        return targets

    def _get_current_account_target(self, year: int) -> float:
        """Get current account target for a specific year"""
        try:
            target_info = self.targets.get_target('current_account_pct_gdp', year)
            return target_info.get('value', 0.0) if target_info else 0.0
        except:
            return 0.0

    def _calculate_current_account_achievement(self, year: int) -> float:
        """Calculate current account achievement for a year"""
        try:
            year_col = str(year)

            # Get current account components
            if 'YEMBPCAACCN' in self.data.df.index:
                current_account = float(self.data.df.loc['YEMBPCAACCN', year_col])
                gdp_usd = float(self.data.df.loc['YEMNYGDPMKTPUS', year_col])

                if gdp_usd > 0:
                    return (current_account / gdp_usd) * 100  # As percentage of GDP

            return 0.0
        except:
            return 0.0

    def _calculate_savings_investment_gap(self, year: int) -> float:
        """Calculate savings-investment gap for crisis economy"""
        try:
            year_col = str(year)

            # Simplified S-I gap calculation
            investment = float(self.data.df.loc['YEMNEGDIFTOTCN', year_col])
            # Approximate savings from GDP - Consumption
            gdp = float(self.data.df.loc['YEMNYGDPMKTPCN', year_col])
            consumption = float(self.data.df.loc['YEMNECONPRVTCN', year_col]) + float(self.data.df.loc['YEMNECONGOVTCN', year_col])

            savings = gdp - consumption
            gap = (savings - investment) / gdp if gdp > 0 else 0.0

            return gap
        except:
            return 0.0

    def _calculate_bop_gap(self, year: int) -> float:
        """Calculate balance of payments gap"""
        try:
            year_col = str(year)

            # BOP components
            current_account = float(self.data.df.loc['YEMBPCAACCN', year_col]) if 'YEMBPCAACCN' in self.data.df.index else 0.0
            capital_account = float(self.data.df.loc['YEMBPKAACCN', year_col]) if 'YEMBPKAACCN' in self.data.df.index else 0.0
            financial_account = float(self.data.df.loc['YEMBPFAACCN', year_col]) if 'YEMBPFAACCN' in self.data.df.index else 0.0

            # BOP should sum to zero (with errors and omissions)
            bop_sum = current_account + capital_account + financial_account

            return bop_sum  # Gap from zero
        except:
            return 0.0

    def _get_other_targets(self, years: List[int]) -> Dict[str, Dict[int, float]]:
        """Get all other targets (non-current account)"""
        targets = {}

        target_types = ['gdp_usd', 'imports_usd', 'exports_usd', 'revenue_pct_gdp',
                       'expenditure_pct_gdp', 'fiscal_balance_pct_gdp', 'inflation_percent']

        for target_type in target_types:
            targets[target_type] = {}
            for year in years:
                try:
                    target_info = self.targets.get_target(target_type, year)
                    targets[target_type][year] = target_info.get('value', 0.0) if target_info else 0.0
                except:
                    targets[target_type][year] = 0.0

        return targets

    def _calculate_target_achievement(self, target_name: str, year: int) -> float:
        """Calculate achievement for a specific target"""
        try:
            year_col = str(year)

            if target_name == 'gdp_usd':
                return float(self.data.df.loc['YEMNYGDPMKTPUS', year_col])
            elif target_name == 'imports_usd':
                return float(self.data.df.loc['YEMNEIMPGNFSUS', year_col])
            elif target_name == 'exports_usd':
                return float(self.data.df.loc['YEMNEEXPGNFSUS', year_col])
            elif target_name == 'revenue_pct_gdp':
                revenue = float(self.data.df.loc['YEMGGREVTOTLCN', year_col])
                gdp = float(self.data.df.loc['YEMNYGDPMKTPCN', year_col])
                return (revenue / gdp) * 100 if gdp > 0 else 0.0
            elif target_name == 'expenditure_pct_gdp':
                expenditure = float(self.data.df.loc['YEMGGEXPTOTLCN', year_col])
                gdp = float(self.data.df.loc['YEMNYGDPMKTPCN', year_col])
                return (expenditure / gdp) * 100 if gdp > 0 else 0.0
            elif target_name == 'fiscal_balance_pct_gdp':
                balance = float(self.data.df.loc['YEMGGBALOVRLCN', year_col])
                gdp = float(self.data.df.loc['YEMNYGDPMKTPCN', year_col])
                return (balance / gdp) * 100 if gdp > 0 else 0.0
            elif target_name == 'inflation_percent':
                # Calculate inflation from GDP deflator
                if year > 2022:
                    current_deflator = float(self.data.df.loc['YEMNYGDPMKTPCD', year_col])
                    prev_deflator = float(self.data.df.loc['YEMNYGDPMKTPCD', str(year-1)])
                    return ((current_deflator / prev_deflator) - 1) * 100 if prev_deflator > 0 else 0.0
                else:
                    return 0.0

            return 0.0
        except:
            return 0.0

    def _validate_identities(self, deflators: Dict, years: List[int]) -> Dict[str, float]:
        """Validate that identities are preserved after optimization"""
        violations = {}

        # Apply deflators temporarily
        original_data = self._backup_data()
        self._apply_deflators_temporarily(deflators, years)

        try:
            # Run validation
            validation_results = self.validator.validate_all(years, critical_only=False)

            for identity_name, result in validation_results.items():
                if isinstance(result, dict):
                    violations[identity_name] = result.get('error_percent', 0.0)
                elif isinstance(result, bool):
                    violations[identity_name] = 0.0 if result else 100.0  # 100% violation if failed
                else:
                    violations[identity_name] = 0.0

        except Exception as e:
            logger.warning(f"Error validating identities: {e}")

        finally:
            self.data.df = original_data

        return violations

    def _calculate_target_achievements(self, deflators: Dict, years: List[int]) -> Dict[str, Dict[int, float]]:
        """Calculate target achievements after applying deflators"""
        achievements = {}

        # Apply deflators temporarily
        original_data = self._backup_data()
        self._apply_deflators_temporarily(deflators, years)

        try:
            target_types = ['gdp_usd', 'imports_usd', 'exports_usd', 'current_account_pct_gdp',
                           'revenue_pct_gdp', 'expenditure_pct_gdp', 'fiscal_balance_pct_gdp', 'inflation_percent']

            for target_type in target_types:
                achievements[target_type] = {}
                for year in years:
                    target = self._get_target_value(target_type, year)
                    achieved = self._calculate_target_achievement(target_type, year)

                    if target > 0:
                        achievements[target_type][year] = (achieved / target) * 100  # Achievement percentage
                    else:
                        achievements[target_type][year] = 100.0 if achieved == 0 else 0.0

        except Exception as e:
            logger.warning(f"Error calculating target achievements: {e}")

        finally:
            self.data.df = original_data

        return achievements

    def _get_target_value(self, target_type: str, year: int) -> float:
        """Get target value for a specific type and year"""
        try:
            target_info = self.targets.get_target(target_type, year)
            return target_info.get('value', 0.0) if target_info else 0.0
        except:
            return 0.0

    def _refine_crisis_gaps(self, deflators: Dict, years: List[int]) -> OptimizationResult:
        """
        Phase 3: Refine crisis economy gaps using adaptive penalties
        while maintaining core identity preservation
        """
        logger.info("Refining crisis economy gaps with adaptive penalties")

        # Check if any crisis gaps need refinement
        crisis_gaps = self._identify_crisis_gaps(deflators, years)

        if not crisis_gaps:
            logger.info("✅ No crisis gaps need refinement")
            return OptimizationResult(
                success=True,
                deflator_adjustments=deflators,
                identity_violations=self._validate_identities(deflators, years),
                target_achievements=self._calculate_target_achievements(deflators, years),
                iterations=0,
                convergence_info={'phase': 'crisis_refinement', 'gaps_refined': False}
            )

        logger.info(f"Refining {len(crisis_gaps)} crisis gaps: {list(crisis_gaps.keys())}")

        # Apply targeted refinements for crisis gaps
        refined_deflators = self._apply_crisis_refinements(deflators, crisis_gaps, years)

        # Final validation
        final_violations = self._validate_identities(refined_deflators, years)
        final_achievements = self._calculate_target_achievements(refined_deflators, years)

        # Check if core identities are still preserved
        core_violations = {k: v for k, v in final_violations.items() if self._is_core_identity(k) and v > self.config.identity_tolerance}

        if core_violations:
            logger.warning(f"⚠️ Core identity violations after crisis refinement: {core_violations}")
            logger.info("Reverting to pre-refinement state to preserve core identities")

            return OptimizationResult(
                success=True,
                deflator_adjustments=deflators,  # Revert to pre-refinement
                identity_violations=self._validate_identities(deflators, years),
                target_achievements=self._calculate_target_achievements(deflators, years),
                iterations=1,
                convergence_info={'phase': 'crisis_refinement', 'reverted': True, 'core_violations': core_violations}
            )
        else:
            logger.info("✅ Crisis gaps refined while preserving core identities")

            return OptimizationResult(
                success=True,
                deflator_adjustments=refined_deflators,
                identity_violations=final_violations,
                target_achievements=final_achievements,
                iterations=1,
                convergence_info={'phase': 'crisis_refinement', 'gaps_refined': True, 'refined_gaps': list(crisis_gaps.keys())}
            )

    def _identify_crisis_gaps(self, deflators: Dict, years: List[int]) -> Dict[str, Dict[int, float]]:
        """Identify crisis economy gaps that need refinement"""
        gaps = {}

        # Apply deflators temporarily
        original_data = self._backup_data()
        self._apply_deflators_temporarily(deflators, years)

        try:
            # Check current account gaps
            current_account_gaps = {}
            for year in years:
                target = self._get_current_account_target(year)
                achieved = self._calculate_current_account_achievement(year)

                if target != 0:
                    gap_percent = abs((achieved - target) / target)
                    if gap_percent > 0.1:  # 10% threshold for refinement
                        current_account_gaps[year] = gap_percent

            if current_account_gaps:
                gaps['current_account'] = current_account_gaps

            # Check other crisis-tolerant gaps
            for year in years:
                si_gap = abs(self._calculate_savings_investment_gap(year))
                if si_gap > self.config.crisis_tolerance:
                    if 'savings_investment' not in gaps:
                        gaps['savings_investment'] = {}
                    gaps['savings_investment'][year] = si_gap

                bop_gap = abs(self._calculate_bop_gap(year))
                if bop_gap > 1000:  # Threshold for BOP gap (in millions USD)
                    if 'bop' not in gaps:
                        gaps['bop'] = {}
                    gaps['bop'][year] = bop_gap

        except Exception as e:
            logger.warning(f"Error identifying crisis gaps: {e}")

        finally:
            self.data.df = original_data

        return gaps

    def _apply_crisis_refinements(self, deflators: Dict, crisis_gaps: Dict, years: List[int]) -> Dict:
        """Apply targeted refinements for identified crisis gaps"""
        refined_deflators = deepcopy(deflators)

        # Current account refinement (DeepSeek's approach)
        if 'current_account' in crisis_gaps:
            logger.info("Applying current account refinements")
            refined_deflators = self._refine_current_account(refined_deflators, crisis_gaps['current_account'], years)

        # Savings-Investment gap refinement
        if 'savings_investment' in crisis_gaps:
            logger.info("Applying savings-investment gap refinements")
            refined_deflators = self._refine_savings_investment(refined_deflators, crisis_gaps['savings_investment'], years)

        # BOP gap refinement
        if 'bop' in crisis_gaps:
            logger.info("Applying BOP gap refinements")
            refined_deflators = self._refine_bop_gap(refined_deflators, crisis_gaps['bop'], years)

        return refined_deflators

    def _refine_current_account(self, deflators: Dict, gaps: Dict[int, float], years: List[int]) -> Dict:
        """Refine current account using DeepSeek's adaptive approach"""
        refined = deepcopy(deflators)

        for year, gap_size in gaps.items():
            if gap_size > 0.2:  # 20% gap threshold for major adjustment
                # Reduce import deflator to improve current account
                if 'YEMNEIMPGNFSCD' in refined:
                    current_import_deflator = refined['YEMNEIMPGNFSCD'].get(year, 100.0)
                    # Adaptive reduction based on gap size
                    reduction_factor = min(0.95, 1.0 - (gap_size * 0.1))  # Max 5% reduction
                    refined['YEMNEIMPGNFSCD'][year] = current_import_deflator * reduction_factor

                    logger.info(f"Reduced import deflator for {year}: {current_import_deflator:.1f} → {refined['YEMNEIMPGNFSCD'][year]:.1f}")

        return refined

    def _refine_savings_investment(self, deflators: Dict, gaps: Dict[int, float], years: List[int]) -> Dict:
        """Refine savings-investment gap within crisis tolerance"""
        refined = deepcopy(deflators)

        for year, gap_size in gaps.items():
            if gap_size > self.config.crisis_tolerance * 2:  # 10% threshold
                # Adjust investment deflator slightly
                if 'YEMNEGDIFTOTCD' in refined:
                    current_investment_deflator = refined['YEMNEGDIFTOTCD'].get(year, 100.0)
                    # Small adjustment to reduce gap
                    adjustment_factor = 1.0 + (gap_size * 0.05)  # Small increase
                    refined['YEMNEGDIFTOTCD'][year] = min(200.0, current_investment_deflator * adjustment_factor)

                    logger.info(f"Adjusted investment deflator for {year}: {current_investment_deflator:.1f} → {refined['YEMNEGDIFTOTCD'][year]:.1f}")

        return refined

    def _refine_bop_gap(self, deflators: Dict, gaps: Dict[int, float], years: List[int]) -> Dict:
        """Refine BOP gap using statistical discrepancy tolerance"""
        refined = deepcopy(deflators)

        # BOP gaps are typically handled through errors and omissions
        # No deflator adjustments needed - let the validator handle with crisis tolerance
        logger.info("BOP gaps will be handled through errors and omissions in validation")

        return refined
