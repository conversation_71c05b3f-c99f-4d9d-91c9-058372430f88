import logging
import pandas as pd
from typing import List, Dict, Any
from copy import deepcopy
from dataclasses import dataclass, field

# To avoid circular imports at runtime, we use TYPE_CHECKING to guard the imports
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from src.data_handler import YemenDataH<PERSON>ler
    from src.target_processor import IMFTargetProcessor
    from src.identity_validator import IdentityValidator
    from src.multi_stage_optimizer import MultiStageOptimizer

@dataclass
class AdjustmentRecord:
    """Stores information about a single adjustment made during alignment."""
    year: int
    variable: str
    adjustment_type: str
    old_value: float
    new_value: float
    percent_change: float

@dataclass
class AlignmentResult:
    """Represents the final result of the alignment process."""
    success: bool
    adjusted_data: pd.DataFrame
    adjustments: List[AdjustmentRecord]
    identity_validation: Dict[str, bool]
    target_achievement: Dict[str, Dict[int, float]]
    error_messages: List[str]

class IdentityPreservingAligner:
    """
    Core engine for managing macroeconomic data and applying identity-preserving adjustments.
    This class does not contain high-level optimization logic but provides the tools
    for an optimizer to use. It maintains the state of the data and validates identities.
    """

    def __init__(self, data_handler: 'YemenDataHandler', target_processor: 'IMFTargetProcessor', tolerance: float = 0.05):
        self.logger = logging.getLogger(__name__)
        self.data = data_handler
        self.targets = target_processor
        from src.identity_validator import IdentityValidator
        self.validator = IdentityValidator(self.data, self.targets.rules)
        self.tolerance = tolerance
        self.adjustments: List[AdjustmentRecord] = []
        self.original_data: pd.DataFrame = None
        self.original_values: Dict[int, Dict[str, float]] = {}

    def align_to_targets(self, years: List[int]) -> 'AlignmentResult':
        """
        Main method to align data to IMF targets. It delegates the complex
        optimization logic to a specialized optimizer class.
        """
        self.logger.info("Starting identity-preserving alignment process.")
        self.original_data = deepcopy(self.data.df)
        self.adjustments = []
        error_messages = []

        # Store original trade values for BOP synchronization
        self._store_original_trade_values(years)

        # Initialize fiscal chains to ensure all totals are properly calculated
        for year in years:
            self._update_fiscal_chain(year)

        from src.multi_stage_optimizer import MultiStageOptimizer
        optimizer = MultiStageOptimizer(self)
        try:
            result = optimizer.run(years, error_messages)
            return result
        except Exception as e:
            self.logger.exception("An unexpected error occurred during the alignment process.")
            error_messages.append(f"Fatal error: {e}")
            self._rollback()
            return AlignmentResult(
                success=False,
                adjusted_data=None,
                adjustments=self.adjustments,
                identity_validation={},
                target_achievement={},
                error_messages=error_messages
            )

    def _store_original_trade_values(self, years: List[int]):
        """Store original trade values for BOP synchronization."""
        for year in years:
            if year not in self.original_values:
                self.original_values[year] = {}

            # Store original NA trade values
            self.original_values[year]['YEMNEEXPGNFSCN'] = self.data.get_variable('YEMNEEXPGNFSCN', [year]).get(year, 0)
            self.original_values[year]['YEMNEIMPGNFSCN'] = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)

            # Store original BOP trade values
            self.original_values[year]['YEMBXGSRMRCHCD'] = self.data.get_variable('YEMBXGSRMRCHCD', [year]).get(year, 0)
            self.original_values[year]['YEMBXGSRNFSVCD'] = self.data.get_variable('YEMBXGSRNFSVCD', [year]).get(year, 0)
            self.original_values[year]['YEMBMGSRMRCHCD'] = self.data.get_variable('YEMBMGSRMRCHCD', [year]).get(year, 0)
            self.original_values[year]['YEMBMGSRNFSVCD'] = self.data.get_variable('YEMBMGSRNFSVCD', [year]).get(year, 0)

    def _rollback(self):
        """Roll back to the original data if the alignment fails."""
        self.logger.info("Rolling back to original data.")
        if self.original_data is not None:
            self.data.df = self.original_data
            self.adjustments = [] # Also clear adjustments
        else:
            self.logger.warning("No original data to roll back to.")

    def _recalculate_full_framework(self, year: int):
        """
        Recalculates the entire macroeconomic framework in the correct sequence.
        This is the central hub for ensuring consistency after any adjustment.
        """
        self.logger.debug(f"Recalculating full framework for year {year}.")
        # The order of these operations is critical to ensure proper cascading of changes.

        # 1. Update aggregates that depend on direct component adjustments (e.g., investment)
        self._recalculate_investment_total(year)

        # 2. Update fiscal chain. This is important because gov_consumption is part of GDP.
        self._update_fiscal_chain(year)

        # 3. Recalculate GDP from the expenditure side
        self._recalculate_gdp_from_components(year)

        # 4. Synchronize the production side of GDP to match the new expenditure-side GDP
        self._synchronize_gdp_production_side(year)

        # 5. Synchronize BOP trade variables with NA trade adjustments
        self._synchronize_bop_from_na(year)

        # 6. Recalculate the current account (BOP)
        self._recalculate_current_account(year)

    def _synchronize_gdp_production_side(self, year: int):
        """Ensures the production side of GDP matches the expenditure side."""
        new_gdp_exp = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year)

        agri = self.data.get_variable('YEMNVAGRTOTLCN', [year]).get(year, 0)
        ind = self.data.get_variable('YEMNVINDTOTLCN', [year]).get(year, 0)
        serv = self.data.get_variable('YEMNVSRVTOTLCN', [year]).get(year, 0)
        net_taxes = self.data.get_variable('YEMNYTAXNINDCN', [year]).get(year, 0)

        gdp_prod_old = agri + ind + serv + net_taxes
        if gdp_prod_old != 0 and new_gdp_exp is not None:
            adj_factor = new_gdp_exp / gdp_prod_old

            # Adjust all components of the production side proportionally
            new_agri = agri * adj_factor
            new_ind = ind * adj_factor
            new_serv = serv * adj_factor
            new_net_taxes = net_taxes * adj_factor

            self.data.update_variable('YEMNVAGRTOTLCN', year, new_agri)
            self.data.update_variable('YEMNVINDTOTLCN', year, new_ind)
            self.data.update_variable('YEMNVSRVTOTLCN', year, new_serv)
            self.data.update_variable('YEMNYTAXNINDCN', year, new_net_taxes)
            self.data.update_variable('YEMNYGDPFCSTCN', year, new_agri + new_ind + new_serv)

    def _recalculate_gdp_from_components(self, year: int):
        """Recalculates GDP (expenditure approach) based on its components."""
        components = [
            'YEMNECONPRVTCN', 'YEMNECONGOVTCN', 'YEMNEGDIFTOTCN',
            'YEMNEGDISTKBCN', 'YEMNEEXPGNFSCN'
        ]
        gdp_calc = sum(self.data.get_variable(comp, [year]).get(year, 0) for comp in components)
        gdp_calc -= self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0) # Subtract imports
        gdp_calc += self.data.get_variable('YEMNYGDPDISCCN', [year]).get(year, 0) # Add stat discrepancy

        self.data.update_variable('YEMNYGDPMKTPCN', year, gdp_calc)
        self.logger.debug(f"Recalculated GDP for {year}: {gdp_calc:,.0f}")

    def _recalculate_investment_total(self, year: int):
        """Recalculates total investment from its public and private components."""
        i_public = self.data.get_variable('YEMNEGDIFGOVCN', [year]).get(year, 0)
        i_private = self.data.get_variable('YEMNEGDIFPRVCN', [year]).get(year, 0)
        i_total = i_public + i_private
        self.data.update_variable('YEMNEGDIFTOTCN', year, i_total)

    def _update_revenue_chain(self, year: int):
        """Updates the entire fiscal revenue chain after a component changes."""
        revenue_components = ['YEMGGREVTAXTCN', 'YEMGGREVGRNTCN', 'YEMGGREVOTHRCN', 'YEMGGREVCOMMCN', 'YEMGGREVINTPCN']
        new_total_revenue = 0
        for comp in revenue_components:
            val = self.data.get_variable(comp, [year]).get(year, 0)
            if pd.notna(val):  # Only add non-NaN values
                new_total_revenue += val
        self.data.update_variable('YEMGGREVTOTLCN', year, new_total_revenue)

    def _update_fiscal_chain(self, year: int):
        """Updates the entire fiscal expenditure and revenue chains after a component changes."""
        # First update revenue chain in case revenue components were adjusted
        self._update_revenue_chain(year)

        # Then update expenditure chain
        current_exp_components = ['YEMGGEXPWAGECN', 'YEMGGEXPGNFSCN', 'YEMGGEXPINTPCN', 'YEMGGEXPTRNSCN', 'YEMGGEXPCROTCN']
        new_current_exp = sum(self.data.get_variable(comp, [year]).get(year, 0) for comp in current_exp_components)
        self.data.update_variable('YEMGGEXPCRNTCN', year, new_current_exp)

        total_exp_components = ['YEMGGEXPCRNTCN', 'YEMGGEXPCAPTCN', 'YEMGGEXPOTHRCN']
        new_total_exp = sum(self.data.get_variable(comp, [year]).get(year, 0) for comp in total_exp_components)
        self.data.update_variable('YEMGGEXPTOTLCN', year, new_total_exp)

        # Now get the updated total revenue
        total_revenue = self.data.get_variable('YEMGGREVTOTLCN', [year]).get(year, 0)
        overall_balance = total_revenue - new_total_exp
        self.data.update_variable('YEMGGBALOVRLCN', year, overall_balance)

        interest_payments = self.data.get_variable('YEMGGEXPINTPCN', [year]).get(year, 0)
        primary_balance = overall_balance + interest_payments
        self.data.update_variable('YEMGGBALPRIMCN', year, primary_balance)

    def _recalculate_current_account(self, year: int):
        """Recalculates the current account balance from its components."""
        exports_goods = self.data.get_variable('YEMBXGSRMRCHCD', [year]).get(year, 0)
        exports_services = self.data.get_variable('YEMBXGSRNFSVCD', [year]).get(year, 0)
        imports_goods = self.data.get_variable('YEMBMGSRMRCHCD', [year]).get(year, 0)
        imports_services = self.data.get_variable('YEMBMGSRNFSVCD', [year]).get(year, 0)
        income_receipts = self.data.get_variable('YEMBXFSTCABTCD', [year]).get(year, 0)
        income_payments = self.data.get_variable('YEMBMFSTCABTCD', [year]).get(year, 0)

        trade_balance = (exports_goods + exports_services) - (imports_goods + imports_services)
        income_balance = income_receipts - income_payments
        ca_new = trade_balance + income_balance
        self.data.update_variable('YEMBNCABFUNDCD', year, ca_new)

    def _get_adjustable_components(self, fiscal_type: str, level: str = 'top') -> list[str]:
        if fiscal_type == 'revenue':
            return ['YEMGGREVTAXTCN', 'YEMGGREVOTHRCN', 'YEMGGREVCOMMCN', 'YEMGGREVINTPCN']
        elif fiscal_type == 'expenditure':
            if level == 'top':
                return ['YEMGGEXPCRNTCN', 'YEMGGEXPCAPTCN', 'YEMGGEXPOTHRCN']
            else: # sub
                return ['YEMGGEXPWAGECN', 'YEMGGEXPGNFSCN', 'YEMGGEXPTRNSCN', 'YEMGGEXPCROTCN']
        return []

    def _adjust_fiscal_components(self, year: int, fiscal_type: str, target_value: float):
        """Adjusts fiscal components to meet a target, respecting fixed components."""
        total_var = 'YEMGGREVTOTLCN' if fiscal_type == 'revenue' else 'YEMGGEXPTOTLCN'

        adjustable_comps = self._get_adjustable_components(fiscal_type, 'top')

        non_adjustable_sum = 0
        if fiscal_type == 'revenue':
            non_adjustable_sum = self.data.get_variable('YEMGGREVGRNTCN', [year]).get(year, 0)

        adjustable_sum = sum(self.data.get_variable(comp, [year]).get(year, 0) for comp in adjustable_comps)

        if adjustable_sum == 0:
            self.logger.warning(f"No adjustable {fiscal_type} components to modify for year {year}")
            return

        adjustable_target = target_value - non_adjustable_sum
        factor = adjustable_target / adjustable_sum if adjustable_sum != 0 else 1.0

        for comp in adjustable_comps:
            old_val = self.data.get_variable(comp, [year]).get(year, 0)
            new_val = old_val * factor
            self.data.update_variable(comp, year, new_val)
            self.adjustments.append(AdjustmentRecord(year, comp, 'fiscal', old_val, new_val, (factor-1)*100))

        if fiscal_type == 'expenditure':
            new_current_exp = self.data.get_variable('YEMGGEXPCRNTCN', [year]).get(year, 0)
            interest = self.data.get_variable('YEMGGEXPINTPCN', [year]).get(year, 0)

            adjustable_sub_comps = self._get_adjustable_components('expenditure', 'sub')
            adjustable_sub_sum = sum(self.data.get_variable(comp, [year]).get(year, 0) for comp in adjustable_sub_comps)

            if adjustable_sub_sum > 0:
                sub_target = new_current_exp - interest
                sub_factor = sub_target / adjustable_sub_sum

                for sub_comp in adjustable_sub_comps:
                    old_sub_val = self.data.get_variable(sub_comp, [year]).get(year, 0)
                    new_sub_val = old_sub_val * sub_factor
                    self.data.update_variable(sub_comp, year, new_sub_val)

        self._update_fiscal_chain(year)
        wages = self.data.get_variable('YEMGGEXPWAGECN', [year]).get(year, 0)
        goods_services = self.data.get_variable('YEMGGEXPGNFSCN', [year]).get(year, 0)
        self.data.update_variable('YEMNECONGOVTCN', year, wages + goods_services)

    def _calculate_private_investment_as_residual(self, year: int, gdp_target_lcu: float):
        """Adjusts private investment to ensure the GDP identity meets the target."""
        other_expenditure_sum = sum(
            self.data.get_variable(comp, [year]).get(year, 0) for comp in
            ['YEMNECONPRVTCN', 'YEMNECONGOVTCN', 'YEMNEGDIFGOVCN', 'YEMNEGDISTKBCN', 'YEMNEEXPGNFSCN']
        )
        other_expenditure_sum -= self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)
        other_expenditure_sum += self.data.get_variable('YEMNYGDPDISCCN', [year]).get(year, 0)

        required_private_investment = gdp_target_lcu - other_expenditure_sum

        old_val = self.data.get_variable('YEMNEGDIFPRVCN', [year]).get(year, 0)
        self.data.update_variable('YEMNEGDIFPRVCN', year, required_private_investment)
        self.adjustments.append(AdjustmentRecord(
            year, 'YEMNEGDIFPRVCN', 'residual_gdp', old_val, required_private_investment,
            ((required_private_investment/old_val - 1)*100) if old_val !=0 else 0
        ))

        self._recalculate_investment_total(year)
        self._recalculate_gdp_from_components(year)

    def _synchronize_bop_from_na(self, year: int):
        """
        CRITICAL: Enforces strict trade consistency for MFMOD compliance.
        Ensures NA Trade (LCU) ÷ Exchange Rate = BOP Trade (USD)

        This is essential to prevent MFMOD team flagging inconsistencies.
        """
        if year not in self.original_values:
            self.logger.warning(f"No original values stored for {year}, skipping BOP synchronization.")
            return

        # Get exchange rate
        exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 0)
        if exchange_rate == 0:
            self.logger.error(f"Cannot synchronize BOP for {year}: exchange rate is zero")
            return

        # Get current NA trade values (in millions LCU)
        na_exports_lcu = self.data.get_variable('YEMNEEXPGNFSCN', [year]).get(year, 0)
        na_imports_lcu = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)

        # Calculate required BOP values (in millions USD)
        # NA (millions LCU) ÷ Exchange Rate (LCU/USD) = BOP (millions USD)
        required_bop_exports_usd = na_exports_lcu / exchange_rate
        required_bop_imports_usd = na_imports_lcu / exchange_rate

        # Get current BOP trade totals to calculate distribution ratios
        current_exports_goods = self.data.get_variable('YEMBXGSRMRCHCD', [year]).get(year, 0)
        current_exports_services = self.data.get_variable('YEMBXGSRNFSVCD', [year]).get(year, 0)
        current_imports_goods = self.data.get_variable('YEMBMGSRMRCHCD', [year]).get(year, 0)
        current_imports_services = self.data.get_variable('YEMBMGSRNFSVCD', [year]).get(year, 0)

        current_bop_exports_total = current_exports_goods + current_exports_services
        current_bop_imports_total = current_imports_goods + current_imports_services

        # Calculate distribution ratios (preserve goods/services split)
        if current_bop_exports_total > 0:
            goods_export_ratio = current_exports_goods / current_bop_exports_total
            services_export_ratio = current_exports_services / current_bop_exports_total
        else:
            goods_export_ratio = 0.8  # Default assumption
            services_export_ratio = 0.2

        if current_bop_imports_total > 0:
            goods_import_ratio = current_imports_goods / current_bop_imports_total
            services_import_ratio = current_imports_services / current_bop_imports_total
        else:
            goods_import_ratio = 0.8  # Default assumption
            services_import_ratio = 0.2

        # Set BOP values to ensure perfect consistency
        new_exports_goods = required_bop_exports_usd * goods_export_ratio
        new_exports_services = required_bop_exports_usd * services_export_ratio
        new_imports_goods = required_bop_imports_usd * goods_import_ratio
        new_imports_services = required_bop_imports_usd * services_import_ratio

        # Update BOP variables
        self.data.update_variable('YEMBXGSRMRCHCD', year, new_exports_goods)
        self.data.update_variable('YEMBXGSRNFSVCD', year, new_exports_services)
        self.data.update_variable('YEMBMGSRMRCHCD', year, new_imports_goods)
        self.data.update_variable('YEMBMGSRNFSVCD', year, new_imports_services)

        # Log the enforcement for transparency
        self.logger.info(f"🔧 {year}: Enforced trade consistency - NA Exports {na_exports_lcu:,.0f} LCU = BOP Exports {required_bop_exports_usd:.1f} USD")
        self.logger.info(f"🔧 {year}: Enforced trade consistency - NA Imports {na_imports_lcu:,.0f} LCU = BOP Imports {required_bop_imports_usd:.1f} USD")

        self._recalculate_current_account(year)

    def _synchronize_bop_trade(self, year, na_change_factor_exports, na_change_factor_imports):
        """Legacy method - kept for backward compatibility."""
        for var in ['YEMBXGSRMRCHCD', 'YEMBXGSRNFSVCD']:
            old_val = self.data.get_variable(var, [year]).get(year, 0)
            self.data.update_variable(var, year, old_val * na_change_factor_exports)

        for var in ['YEMBMGSRMRCHCD', 'YEMBMGSRNFSVCD']:
            old_val = self.data.get_variable(var, [year]).get(year, 0)
            self.data.update_variable(var, year, old_val * na_change_factor_imports)

        self._recalculate_current_account(year)

    def _calculate_target_achievement(self, years: List[int]) -> Dict[str, Dict[int, float]]:
        """
        Calculate how well each IMF target was achieved.
        """
        achievement = {}
        self.logger.info("Calculating final target achievement.")

        # GDP targets
        gdp_targets = self.targets.get_gdp_targets(years)
        achievement['gdp_usd'] = {}
        for year in years:
            if year in gdp_targets and pd.notna(gdp_targets[year]):
                target_gdp_usd = gdp_targets[year]
                gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year)
                exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year)
                if pd.notna(gdp_nominal) and pd.notna(exchange_rate) and exchange_rate > 0 and target_gdp_usd > 0:
                    actual_gdp_usd = (gdp_nominal / exchange_rate) / 1000  # convert to billions
                    achievement['gdp_usd'][year] = (actual_gdp_usd / target_gdp_usd) * 100

        # Import targets
        import_targets = self.targets.get_trade_targets(years).get('imports', {})
        achievement['imports_usd'] = {}
        for year in years:
            if year in import_targets and pd.notna(import_targets[year]):
                target_imports_usd = import_targets[year]
                imports_nominal = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year)
                exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year)
                if pd.notna(imports_nominal) and pd.notna(exchange_rate) and exchange_rate > 0 and target_imports_usd > 0:
                    actual_imports_usd = (imports_nominal / exchange_rate) / 1000  # convert to billions
                    achievement['imports_usd'][year] = (actual_imports_usd / target_imports_usd) * 100

        # Fiscal targets
        fiscal_targets = self.targets.get_fiscal_targets(years)
        achievement['revenue_pct_gdp'] = {}
        achievement['expenditure_pct_gdp'] = {}
        for year in years:
            gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year)
            if pd.notna(gdp_nominal) and gdp_nominal > 0:
                # Revenue
                target_pct = fiscal_targets.get('revenue', {}).get(year)
                if pd.notna(target_pct):
                    revenue = self.data.get_variable('YEMGGREVTOTLCN', [year]).get(year)
                    if pd.notna(revenue):
                        actual_pct = (revenue / gdp_nominal) * 100
                        achievement['revenue_pct_gdp'][year] = (actual_pct / target_pct) * 100 if target_pct != 0 else 0
                # Expenditure
                target_pct = fiscal_targets.get('expenditure', {}).get(year)
                if pd.notna(target_pct):
                    expenditure = self.data.get_variable('YEMGGEXPTOTLCN', [year]).get(year)
                    if pd.notna(expenditure):
                        actual_pct = (expenditure / gdp_nominal) * 100
                        achievement['expenditure_pct_gdp'][year] = (actual_pct / target_pct) * 100 if target_pct != 0 else 0

        # Inflation targets
        inflation_targets = self.targets.get_inflation_targets(years)
        achievement['inflation_percent'] = {}
        for year in years:
            if year in inflation_targets and pd.notna(inflation_targets[year]):
                target_inflation = inflation_targets[year]
                cpi_current = self.data.get_variable('YEMFPCPITOTLXN', [year]).get(year)
                cpi_previous = self.data.get_variable('YEMFPCPITOTLXN', [year - 1]).get(year - 1)
                if pd.notna(cpi_current) and pd.notna(cpi_previous) and cpi_previous != 0:
                    actual_inflation = ((cpi_current - cpi_previous) / cpi_previous) * 100
                    deviation = abs(actual_inflation - target_inflation)
                    if abs(target_inflation) > 0:
                        achievement_pct = max(0, 100 - (deviation / abs(target_inflation) * 100))
                    else:
                        achievement_pct = 100 if deviation < 1 else 0 # 1% tolerance if target is 0
                    achievement['inflation_percent'][year] = achievement_pct

        # Current account targets
        ca_targets = self.targets.get_current_account_targets(years)
        achievement['current_account_pct_gdp'] = {}
        for year in years:
            if year in ca_targets and pd.notna(ca_targets[year]):
                target_ca_pct = ca_targets[year]
                ca_balance = self.data.get_variable('YEMBNCABFUNDCD', [year]).get(year)
                gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year)
                exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year)
                if pd.notna(ca_balance) and pd.notna(gdp_nominal) and pd.notna(exchange_rate) and exchange_rate > 0:
                    gdp_usd = gdp_nominal / exchange_rate
                    actual_ca_pct = (ca_balance / gdp_usd) * 100 if gdp_usd != 0 else 0
                    achievement['current_account_pct_gdp'][year] = (actual_ca_pct / target_ca_pct) * 100 if target_ca_pct != 0 else 0

        # Export targets
        export_targets = self.targets.get_export_targets(years)
        achievement['exports_usd'] = {}
        for year in years:
            if year in export_targets and pd.notna(export_targets[year]):
                target_exports_usd = export_targets[year]
                exports_nominal = self.data.get_variable('YEMNEEXPGNFSCN', [year]).get(year)
                exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year)
                if pd.notna(exports_nominal) and pd.notna(exchange_rate) and exchange_rate > 0 and target_exports_usd > 0:
                    actual_exports_usd = (exports_nominal / exchange_rate) / 1000 # billions
                    achievement['exports_usd'][year] = (actual_exports_usd / target_exports_usd) * 100

        # Fiscal balance targets
        fiscal_balance_targets = self.targets.get_fiscal_balance_targets(years)
        achievement['fiscal_balance_pct_gdp'] = {}
        for year in years:
            if year in fiscal_balance_targets and pd.notna(fiscal_balance_targets[year]):
                target_balance_pct = fiscal_balance_targets[year]
                fiscal_balance = self.data.get_variable('YEMGGBALOVRLCN', [year]).get(year)
                gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year)
                if pd.notna(fiscal_balance) and pd.notna(gdp_nominal) and gdp_nominal > 0:
                    actual_balance_pct = (fiscal_balance / gdp_nominal) * 100
                    achievement['fiscal_balance_pct_gdp'][year] = (actual_balance_pct / target_balance_pct) * 100 if target_balance_pct != 0 else 0

        return achievement

    def generate_alignment_report(self, result: AlignmentResult) -> str:
        """
        Generate a comprehensive report of the alignment process

        Args:
            result: AlignmentResult from align_to_targets

        Returns:
            Markdown-formatted report
        """
        report = []
        report.append("# Identity-Preserving IMF Alignment Report")
        report.append("")
        report.append(f"**Status**: {'✅ SUCCESS' if result.success else '❌ FAILED'}")
        report.append("")

        # Summary
        report.append("## Summary")
        report.append(f"- Total adjustments made: {len(result.adjustments)}")
        if result.identity_validation:
            report.append(f"- Identity validation: {sum(result.identity_validation.values())}/{len(result.identity_validation)} passed")

        if result.error_messages:
            report.append("")
            report.append("### Errors")
            for error in result.error_messages:
                report.append(f"- {error}")

        # Adjustments detail
        report.append("")
        report.append("## Adjustments Made")

        # Group by year
        adjustments_by_year = {}
        for adj in result.adjustments:
            year = adj.year
            if year not in adjustments_by_year:
                adjustments_by_year[year] = []
            adjustments_by_year[year].append(adj)

        for year in sorted(adjustments_by_year.keys()):
            report.append(f"### Year {year}")
            for adj in adjustments_by_year[year]:
                report.append(f"- **{adj.variable}**: {adj.old_value:,.0f} → {adj.new_value:,.0f} ({adj.percent_change:+.1f}%)")

        # Identity validation
        if result.identity_validation:
            report.append("")
            report.append("## Identity Validation")
            for identity, valid in result.identity_validation.items():
                status = "✅" if valid else "❌"
                report.append(f"- {identity}: {status}")

        # Target achievement
        if result.target_achievement:
            report.append("")
            report.append("## Target Achievement")
            for target_type, years_data in result.target_achievement.items():
                report.append(f"\n### {target_type}")
                for year, achievement in years_data.items():
                    status = "✅" if 95 <= achievement <= 105 else "⚠️"
                    report.append(f"- {year}: {achievement:.1f}% {status}")

        return "\n".join(report)
