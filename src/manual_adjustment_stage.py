"""
Manual Adjustment Stage for Fine-Tuning Targets

This module provides Stage 7 of the optimization process, allowing manual fine-tuning
of specific targets while automatically adjusting all dependent variables through
economic identities.
"""

import logging
from typing import Dict, List, Optional, Tuple
import pandas as pd
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ManualAdjustment:
    """Configuration for a manual adjustment."""
    variable: str
    year: int
    target_value: Optional[float] = None
    target_percentage_change: Optional[float] = None
    adjustment_method: str = 'direct'  # 'direct', 'proportional', 'goal_seek'
    description: str = ""

class ManualAdjustmentStage:
    """
    Stage 7: Manual Fine-Tuning

    Allows targeted adjustments to specific variables while maintaining all
    economic identities through automatic cascading updates.
    """

    def __init__(self, data_handler, validator, aligner, optimizer=None):
        self.data_handler = data_handler
        self.validator = validator
        self.aligner = aligner
        self.optimizer = optimizer  # Reference to MultiStageOptimizer for goal-seek
        self.adjustments_made = []
        
    def apply_manual_adjustments(self, adjustments: List[ManualAdjustment], years: List[int]) -> bool:
        """
        Apply a list of manual adjustments while preserving all identities.
        
        Args:
            adjustments: List of manual adjustments to apply
            years: Years to validate after adjustments
            
        Returns:
            bool: True if all adjustments successful and identities preserved
        """
        logger.info("=== STAGE 7: Manual Fine-Tuning ===")
        
        success = True
        
        for adjustment in adjustments:
            logger.info(f"Applying manual adjustment: {adjustment.description}")
            
            try:
                if adjustment.adjustment_method == 'direct':
                    self._apply_direct_adjustment(adjustment)
                elif adjustment.adjustment_method == 'proportional':
                    self._apply_proportional_adjustment(adjustment)
                elif adjustment.adjustment_method == 'goal_seek':
                    self._apply_goal_seek_adjustment(adjustment)
                else:
                    logger.error(f"Unknown adjustment method: {adjustment.adjustment_method}")
                    success = False
                    continue
                    
                # Validate identities after each adjustment
                validation_result = self.validator.validate_all([adjustment.year], critical_only=True)
                all_valid = all(validation_result.values())
                if not all_valid:
                    failed = [k for k, v in validation_result.items() if not v]
                    logger.error(f"Identity validation failed after adjustment: {adjustment.description}")
                    logger.error(f"Failed identities: {', '.join(failed)}")
                    # Rollback this adjustment
                    self.aligner._rollback()
                    success = False
                else:
                    logger.info(f"✅ Manual adjustment successful: {adjustment.description}")
                    self.adjustments_made.append(adjustment)
                    
            except Exception as e:
                logger.error(f"Error applying adjustment {adjustment.description}: {e}")
                success = False
                
        # Final validation across all years
        if success:
            final_validation = self.validator.validate_all(years, critical_only=False)
            if not final_validation['all_valid']:
                logger.error("Final validation failed after manual adjustments")
                success = False
            else:
                logger.info("✅ All manual adjustments completed successfully")
                
        return success
    
    def _apply_direct_adjustment(self, adjustment: ManualAdjustment):
        """Apply a direct value adjustment."""
        if adjustment.target_value is not None:
            self.data_handler.update_variable(
                adjustment.variable, 
                adjustment.year, 
                adjustment.target_value
            )
        elif adjustment.target_percentage_change is not None:
            current_value = self.data_handler.get_variable(
                adjustment.variable, 
                [adjustment.year]
            ).get(adjustment.year)
            
            if current_value is not None:
                new_value = current_value * (1 + adjustment.target_percentage_change / 100)
                self.data_handler.update_variable(
                    adjustment.variable, 
                    adjustment.year, 
                    new_value
                )
            else:
                raise ValueError(f"Cannot find current value for {adjustment.variable} in {adjustment.year}")
        else:
            raise ValueError("Either target_value or target_percentage_change must be specified")
    
    def _apply_proportional_adjustment(self, adjustment: ManualAdjustment):
        """Apply a proportional adjustment based on related variables."""
        # This could be enhanced to adjust multiple related variables proportionally
        self._apply_direct_adjustment(adjustment)
    
    def _apply_goal_seek_adjustment(self, adjustment: ManualAdjustment):
        """Apply adjustment using goal-seek methodology."""
        target_value = adjustment.target_value

        # If percentage change is specified, calculate the target value
        if target_value is None and adjustment.target_percentage_change is not None:
            current_value = self.data_handler.get_variable(
                adjustment.variable,
                [adjustment.year]
            ).get(adjustment.year)

            if current_value is not None:
                target_value = current_value * (1 + adjustment.target_percentage_change / 100)
            else:
                raise ValueError(f"Cannot find current value for {adjustment.variable} in {adjustment.year}")

        if target_value is not None:
            # Choose appropriate instrument variable based on target
            if adjustment.variable == 'YEMNYGDPMKTPCN':
                instrument_var = 'YEMNEGDIFPRVCN'  # Private investment for GDP
            elif adjustment.variable == 'YEMBNCABFUNDCD':
                instrument_var = 'YEMNEIMPGNFSCN'  # Imports for current account (this works)
            else:
                instrument_var = adjustment.variable  # Default to self-adjustment

            if self.optimizer is not None:
                success = self.optimizer._goal_seek(
                    year=adjustment.year,
                    target_variable=adjustment.variable,
                    target_value=target_value,
                    instrument_variable=instrument_var
                )
                if not success:
                    raise ValueError(f"Goal-seek failed for {adjustment.variable} in {adjustment.year}")
            else:
                raise ValueError("Optimizer reference required for goal-seek method")
        else:
            raise ValueError("Either target_value or target_percentage_change must be specified for goal_seek method")

def create_gdp_boost_adjustments(years: List[int], boost_percentage: float = 10.0) -> List[ManualAdjustment]:
    """
    Create adjustments to boost GDP achievement.

    Strategy: Use goal-seek to adjust GDP targets upward while maintaining all identities.
    This uses the same mechanism as the automatic stages but with higher targets.
    """
    adjustments = []

    for year in years:
        # Use goal-seek to boost GDP (maintains all identities through existing framework)
        adjustments.append(ManualAdjustment(
            variable='YEMNYGDPMKTPCN',  # GDP nominal
            year=year,
            target_percentage_change=boost_percentage,
            adjustment_method='goal_seek',
            description=f"Boost GDP by {boost_percentage}% using goal-seek in {year}"
        ))

    return adjustments

def create_fiscal_balance_adjustments(years: List[int], target_improvements: Dict[int, float]) -> List[ManualAdjustment]:
    """
    Create adjustments to improve fiscal balance achievement.
    
    Strategy: Adjust government expenditure to meet fiscal balance targets.
    """
    adjustments = []
    
    for year, improvement_pct in target_improvements.items():
        if year in years:
            adjustments.append(ManualAdjustment(
                variable='YEMGGEXPTOTLCN',  # Total government expenditure
                year=year,
                target_percentage_change=-improvement_pct,  # Reduce expenditure
                adjustment_method='proportional',
                description=f"Reduce government expenditure by {improvement_pct}% to improve fiscal balance in {year}"
            ))
    
    return adjustments

def create_current_account_adjustments(years: List[int], target_ca_data: Dict[int, Dict]) -> List[ManualAdjustment]:
    """
    Create adjustments to align current account deficit using secondary account transfers.

    Strategy: Adjust secondary income transfers to meet current account targets.
    """
    adjustments = []

    for year in years:
        if year in target_ca_data:
            data = target_ca_data[year]
            target_ca_usd = data['target_ca_usd']
            target_ca_pct = data['target_ca_pct']

            adjustments.append(ManualAdjustment(
                variable='YEMBNCABFUNDCD',  # Current account balance
                year=year,
                target_value=target_ca_usd,
                adjustment_method='goal_seek',
                description=f"Adjust secondary transfers to achieve {target_ca_pct}% current account deficit in {year}"
            ))

    return adjustments

def create_fiscal_revenue_adjustments(years: List[int], target_improvements: Dict[int, float]) -> List[ManualAdjustment]:
    """
    Create adjustments to improve fiscal revenues using other revenues and grants.

    Strategy: Adjust other fiscal revenues and grants to meet revenue targets.
    """
    adjustments = []

    for year, improvement_pct in target_improvements.items():
        if year in years:
            adjustments.append(ManualAdjustment(
                variable='YEMGGREVGRNTCN',  # Government grants
                year=year,
                target_percentage_change=improvement_pct,
                adjustment_method='proportional',
                description=f"Increase grants by {improvement_pct}% to improve fiscal revenue in {year}"
            ))

            adjustments.append(ManualAdjustment(
                variable='YEMGGREVOTHRCN',  # Other revenues
                year=year,
                target_percentage_change=improvement_pct,
                adjustment_method='proportional',
                description=f"Increase other revenues by {improvement_pct}% to improve fiscal revenue in {year}"
            ))

    return adjustments

def create_trade_balance_adjustments(years: List[int], export_boost: float = 5.0) -> List[ManualAdjustment]:
    """
    Create adjustments to fine-tune trade balance.

    Strategy: Small adjustments to exports to balance current account.
    """
    adjustments = []

    for year in years:
        adjustments.append(ManualAdjustment(
            variable='YEMNEEXPGNFSCN',  # Exports USD
            year=year,
            target_percentage_change=export_boost,
            adjustment_method='proportional',
            description=f"Fine-tune exports by {export_boost}% to balance current account in {year}"
        ))

    return adjustments
