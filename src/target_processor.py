"""
Process IMF targets and compare with WB values
"""

import yaml
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging

logger = logging.getLogger(__name__)


class IMFTargetProcessor:
    """Process IMF targets and calculate required adjustments"""

    def __init__(self, targets_path: str, rules_path: str):
        """Initialize target processor

        Args:
            targets_path: Path to IMF targets YAML
            rules_path: Path to adjustment rules YAML
        """
        self.targets = self._load_yaml(targets_path)
        self.rules = self._load_yaml(rules_path)

    def _load_yaml(self, path: str) -> Dict:
        """Load YAML configuration file"""
        with open(path, 'r') as f:
            return yaml.safe_load(f)

    def get_gdp_targets(self, years: List[int]) -> Dict[int, float]:
        """Get GDP nominal targets in USD billions

        Args:
            years: List of years

        Returns:
            Dict of year: target_value
        """
        gdp_targets = self.targets['real_sector']['gdp_nominal_usd_billions']
        return {year: gdp_targets.get(year, np.nan) for year in years}

    def get_fiscal_targets(self, years: List[int]) -> Dict[str, Dict[int, float]]:
        """Get fiscal targets as percent of GDP

        Args:
            years: List of years

        Returns:
            Dict with 'revenue' and 'expenditure' targets
        """
        fiscal = self.targets['fiscal_sector']

        result = {
            'revenue': {},
            'expenditure': {}
        }

        for year in years:
            # Handle 2022 revenue preservation rule
            if year == 2022 and self.rules['constraints']['preserve_2022_fiscal_revenue']:
                result['revenue'][year] = None  # Signal to preserve WB value
            else:
                result['revenue'][year] = fiscal['revenue_percent_gdp'].get(year, np.nan)

            result['expenditure'][year] = fiscal['expenditure_percent_gdp'].get(year, np.nan)

        return result

    def get_trade_targets(self, years: List[int]) -> Dict[str, Dict[int, float]]:
        """Get trade targets in USD billions

        Args:
            years: List of years

        Returns:
            Dict with 'exports' and 'imports' targets
        """
        external = self.targets['external_sector']

        # For exports, we use non-oil targets since oil is fixed at WB levels
        result = {
            'exports_total': {year: external['exports_total_usd_billions'].get(year, np.nan)
                             for year in years},
            'exports_non_oil': {year: external['non_oil_exports_usd_billions'].get(year, np.nan)
                               for year in years},
            'imports': {year: external['imports_usd_billions'].get(year, np.nan)
                       for year in years}
        }

        return result

    def get_inflation_targets(self, years: List[int]) -> Dict[int, float]:
        """Get CPI inflation targets

        Args:
            years: List of years

        Returns:
            Dict of year: inflation_rate
        """
        monetary = self.targets['monetary_sector']
        return {year: monetary['cpi_inflation_percent'].get(year, np.nan) for year in years}

    def get_current_account_targets(self, years: List[int]) -> Dict[int, float]:
        """Get current account balance targets as % of GDP"""
        external = self.targets['external_sector']
        return {year: external['current_account_percent_gdp'].get(year, np.nan) for year in years}

    def get_export_targets(self, years: List[int]) -> Dict[int, float]:
        """Get export targets in billions USD"""
        external = self.targets['external_sector']
        return {year: external['exports_total_usd_billions'].get(year, np.nan) for year in years}

    def get_fiscal_balance_targets(self, years: List[int]) -> Dict[int, float]:
        """Get fiscal balance targets as % of GDP"""
        fiscal = self.targets['fiscal_sector']
        return {year: fiscal['fiscal_balance_percent_gdp'].get(year, np.nan) for year in years}

    def calculate_required_adjustments(self, wb_values: Dict, years: List[int]) -> Dict[str, Dict[int, float]]:
        """Calculate required adjustments to align WB with IMF

        Args:
            wb_values: Current WB values
            years: Years to calculate

        Returns:
            Dict of required adjustments by category
        """
        adjustments = {
            'gdp_deflator': {},
            'import_deflator': {},
            'fiscal_revenue': {},
            'fiscal_expenditure': {},
            'cpi': {}
        }

        # GDP deflator adjustments (from rules)
        gdp_adj = self.rules['adjustments_needed']['gdp_deflator']
        for year in years:
            adjustments['gdp_deflator'][year] = gdp_adj.get(year, 0.0)

        # Import deflator adjustments (from rules)
        import_adj = self.rules['adjustments_needed']['import_deflator']
        for year in years:
            adjustments['import_deflator'][year] = import_adj.get(year, 0.0)

        # Fiscal adjustments (percentage point changes)
        fiscal_adj = self.rules['adjustments_needed']['fiscal_adjustments']
        adjustments['fiscal_revenue'][2023] = fiscal_adj.get('revenue_2023', 0.0)
        adjustments['fiscal_expenditure'][2023] = fiscal_adj.get('expenditure_2023', 0.0)
        adjustments['fiscal_expenditure'][2025] = fiscal_adj.get('expenditure_2025', 0.0)

        # CPI adjustment for 2024
        if self.rules['adjustments_needed']['cpi_adjustment'].get(2024, False):
            # Calculate required CPI change to achieve 33.9% inflation
            # This requires more complex calculation based on previous year CPI
            adjustments['cpi'][2024] = 'calculate'  # Flag for special handling

        return adjustments

    def compare_wb_imf(self, wb_values: Dict, years: List[int]) -> pd.DataFrame:
        """Create comparison table of WB vs IMF values

        Args:
            wb_values: Current WB values
            years: Years to compare

        Returns:
            DataFrame with comparison
        """
        comparisons = []

        # GDP comparison
        gdp_targets = self.get_gdp_targets(years)
        for year in years:
            if year in wb_values.get('gdp_nominal_usd', {}):
                comparisons.append({
                    'Category': 'GDP Nominal',
                    'Year': year,
                    'WB Value': wb_values['gdp_nominal_usd'][year],
                    'IMF Target': gdp_targets[year],
                    'Gap': gdp_targets[year] - wb_values['gdp_nominal_usd'][year],
                    'Gap %': ((gdp_targets[year] - wb_values['gdp_nominal_usd'][year]) /
                             wb_values['gdp_nominal_usd'][year] * 100) if wb_values['gdp_nominal_usd'][year] != 0 else 0
                })

        # Add other comparisons (imports, fiscal, etc.)
        # ... (similar structure for other indicators)

        return pd.DataFrame(comparisons)
