"""
Trade Consistency Enforcer
Ensures strict mathematical relationship: NA Trade (LCU) ÷ Exchange Rate = BOP Trade (USD)

This module is critical for MFMOD compliance and prevents flagging by the MFMOD team.
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class TradeConsistencyReport:
    """Report on trade consistency enforcement"""
    year: int
    na_exports_lcu: float
    na_imports_lcu: float
    bop_exports_usd: float
    bop_imports_usd: float
    exchange_rate: float
    export_ratio: float
    import_ratio: float
    mfmod_compliant: bool
    adjustments_made: List[str]

class TradeConsistencyEnforcer:
    """
    Enforces strict trade consistency for MFMOD compliance.
    
    Key Principle: NA Trade (LCU) ÷ Exchange Rate = BOP Trade (USD)
    
    This ensures that when the MFMOD team validates the data, they will find
    perfect consistency between National Accounts and Balance of Payments trade data.
    """
    
    def __init__(self, data_handler):
        self.data = data_handler
        self.enforcement_reports = []
        
    def enforce_trade_consistency(self, years: List[int]) -> Dict[str, Any]:
        """
        Main method to enforce strict trade consistency across all years.
        
        Returns:
            Dict with enforcement results and compliance status
        """
        logger.info("🔧 Starting MFMOD trade consistency enforcement")
        
        results = {
            'success': True,
            'mfmod_compliant': True,
            'enforcement_reports': [],
            'adjustments_made': [],
            'error_messages': []
        }
        
        try:
            for year in years:
                logger.info(f"Enforcing trade consistency for {year}")
                
                # Enforce consistency for this year
                report = self._enforce_year_consistency(year)
                results['enforcement_reports'].append(report)
                
                if not report.mfmod_compliant:
                    results['mfmod_compliant'] = False
                    results['error_messages'].append(
                        f"Year {year}: Trade consistency not MFMOD compliant"
                    )
                
                if report.adjustments_made:
                    results['adjustments_made'].extend(report.adjustments_made)
                    
            if results['mfmod_compliant']:
                logger.info("✅ All years are MFMOD compliant for trade consistency")
            else:
                logger.warning("⚠️ Some years may be flagged by MFMOD team")
                
        except Exception as e:
            logger.error(f"Trade consistency enforcement failed: {e}")
            results['success'] = False
            results['error_messages'].append(str(e))
            
        return results
    
    def _enforce_year_consistency(self, year: int) -> TradeConsistencyReport:
        """
        Enforce trade consistency for a specific year.
        
        Returns:
            TradeConsistencyReport with enforcement details
        """
        # Get exchange rate
        exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 0)
        if exchange_rate == 0:
            raise ValueError(f"Exchange rate for {year} is zero or missing")
        
        # Get current NA trade values (in millions LCU)
        na_exports_lcu = self.data.get_variable('YEMNEEXPGNFSCN', [year]).get(year, 0)
        na_imports_lcu = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)
        
        # Calculate what BOP values should be (in millions USD)
        required_bop_exports_usd = na_exports_lcu / exchange_rate
        required_bop_imports_usd = na_imports_lcu / exchange_rate
        
        # Get current BOP values
        current_exports_goods = self.data.get_variable('YEMBXGSRMRCHCD', [year]).get(year, 0)
        current_exports_services = self.data.get_variable('YEMBXGSRNFSVCD', [year]).get(year, 0)
        current_imports_goods = self.data.get_variable('YEMBMGSRMRCHCD', [year]).get(year, 0)
        current_imports_services = self.data.get_variable('YEMBMGSRNFSVCD', [year]).get(year, 0)
        
        current_bop_exports_total = current_exports_goods + current_exports_services
        current_bop_imports_total = current_imports_goods + current_imports_services
        
        # Calculate current ratios
        export_ratio = (current_bop_exports_total * exchange_rate) / na_exports_lcu if na_exports_lcu > 0 else 1.0
        import_ratio = (current_bop_imports_total * exchange_rate) / na_imports_lcu if na_imports_lcu > 0 else 1.0
        
        # Check if already MFMOD compliant (within 0.1% tolerance)
        export_compliant = 0.999 <= export_ratio <= 1.001
        import_compliant = 0.999 <= import_ratio <= 1.001
        mfmod_compliant = export_compliant and import_compliant
        
        adjustments_made = []
        
        if not mfmod_compliant:
            # Enforce strict consistency by adjusting BOP values
            adjustments_made.extend(self._adjust_bop_to_match_na(
                year, required_bop_exports_usd, required_bop_imports_usd,
                current_exports_goods, current_exports_services,
                current_imports_goods, current_imports_services
            ))
            
            # Recalculate ratios after adjustment
            export_ratio = 1.0  # Perfect by construction
            import_ratio = 1.0  # Perfect by construction
            mfmod_compliant = True
        
        # Create report
        report = TradeConsistencyReport(
            year=year,
            na_exports_lcu=na_exports_lcu,
            na_imports_lcu=na_imports_lcu,
            bop_exports_usd=required_bop_exports_usd,
            bop_imports_usd=required_bop_imports_usd,
            exchange_rate=exchange_rate,
            export_ratio=export_ratio,
            import_ratio=import_ratio,
            mfmod_compliant=mfmod_compliant,
            adjustments_made=adjustments_made
        )
        
        # Log enforcement result
        if mfmod_compliant:
            logger.info(f"✅ {year}: MFMOD compliant - Export ratio: {export_ratio:.6f}, Import ratio: {import_ratio:.6f}")
        else:
            logger.warning(f"⚠️ {year}: NOT MFMOD compliant - Export ratio: {export_ratio:.3f}, Import ratio: {import_ratio:.3f}")
        
        return report
    
    def _adjust_bop_to_match_na(self, year: int, required_exports_usd: float, required_imports_usd: float,
                               current_exports_goods: float, current_exports_services: float,
                               current_imports_goods: float, current_imports_services: float) -> List[str]:
        """
        Adjust BOP trade values to match NA values exactly.
        
        Returns:
            List of adjustments made
        """
        adjustments = []
        
        # Calculate current totals and ratios
        current_exports_total = current_exports_goods + current_exports_services
        current_imports_total = current_imports_goods + current_imports_services
        
        # Preserve goods/services distribution ratios
        if current_exports_total > 0:
            goods_export_ratio = current_exports_goods / current_exports_total
            services_export_ratio = current_exports_services / current_exports_total
        else:
            goods_export_ratio = 0.8  # Default assumption
            services_export_ratio = 0.2
            
        if current_imports_total > 0:
            goods_import_ratio = current_imports_goods / current_imports_total
            services_import_ratio = current_imports_services / current_imports_total
        else:
            goods_import_ratio = 0.8  # Default assumption
            services_import_ratio = 0.2
        
        # Calculate new BOP values
        new_exports_goods = required_exports_usd * goods_export_ratio
        new_exports_services = required_exports_usd * services_export_ratio
        new_imports_goods = required_imports_usd * goods_import_ratio
        new_imports_services = required_imports_usd * services_import_ratio
        
        # Update BOP variables
        self.data.update_variable('YEMBXGSRMRCHCD', year, new_exports_goods)
        self.data.update_variable('YEMBXGSRNFSVCD', year, new_exports_services)
        self.data.update_variable('YEMBMGSRMRCHCD', year, new_imports_goods)
        self.data.update_variable('YEMBMGSRNFSVCD', year, new_imports_services)
        
        # Record adjustments
        adjustments.append(f"Adjusted YEMBXGSRMRCHCD from {current_exports_goods:.1f} to {new_exports_goods:.1f}")
        adjustments.append(f"Adjusted YEMBXGSRNFSVCD from {current_exports_services:.1f} to {new_exports_services:.1f}")
        adjustments.append(f"Adjusted YEMBMGSRMRCHCD from {current_imports_goods:.1f} to {new_imports_goods:.1f}")
        adjustments.append(f"Adjusted YEMBMGSRNFSVCD from {current_imports_services:.1f} to {new_imports_services:.1f}")
        
        logger.info(f"🔧 {year}: Enforced perfect trade consistency")
        logger.info(f"   Exports: {required_exports_usd:.1f} USD = Goods({new_exports_goods:.1f}) + Services({new_exports_services:.1f})")
        logger.info(f"   Imports: {required_imports_usd:.1f} USD = Goods({new_imports_goods:.1f}) + Services({new_imports_services:.1f})")
        
        return adjustments
    
    def validate_consistency(self, years: List[int]) -> Dict[str, Any]:
        """
        Validate that trade consistency is maintained across all years.
        
        Returns:
            Dict with validation results
        """
        validation_results = {
            'all_compliant': True,
            'year_results': {},
            'summary': {}
        }
        
        for year in years:
            year_result = self._validate_year_consistency(year)
            validation_results['year_results'][year] = year_result
            
            if not year_result['mfmod_compliant']:
                validation_results['all_compliant'] = False
        
        # Generate summary
        compliant_years = sum(1 for r in validation_results['year_results'].values() if r['mfmod_compliant'])
        total_years = len(years)
        
        validation_results['summary'] = {
            'compliant_years': compliant_years,
            'total_years': total_years,
            'compliance_rate': (compliant_years / total_years) * 100 if total_years > 0 else 0
        }
        
        return validation_results
    
    def _validate_year_consistency(self, year: int) -> Dict[str, Any]:
        """Validate trade consistency for a specific year"""
        try:
            exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 0)
            if exchange_rate == 0:
                return {'mfmod_compliant': False, 'error': 'Exchange rate is zero'}
            
            # Get values
            na_exports = self.data.get_variable('YEMNEEXPGNFSCN', [year]).get(year, 0)
            na_imports = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)
            
            bop_exports_goods = self.data.get_variable('YEMBXGSRMRCHCD', [year]).get(year, 0)
            bop_exports_services = self.data.get_variable('YEMBXGSRNFSVCD', [year]).get(year, 0)
            bop_imports_goods = self.data.get_variable('YEMBMGSRMRCHCD', [year]).get(year, 0)
            bop_imports_services = self.data.get_variable('YEMBMGSRNFSVCD', [year]).get(year, 0)
            
            bop_exports_total = bop_exports_goods + bop_exports_services
            bop_imports_total = bop_imports_goods + bop_imports_services
            
            # Calculate ratios
            export_ratio = (bop_exports_total * exchange_rate) / na_exports if na_exports > 0 else 1.0
            import_ratio = (bop_imports_total * exchange_rate) / na_imports if na_imports > 0 else 1.0
            
            # Check MFMOD compliance (0.1% tolerance)
            export_compliant = 0.999 <= export_ratio <= 1.001
            import_compliant = 0.999 <= import_ratio <= 1.001
            mfmod_compliant = export_compliant and import_compliant
            
            return {
                'mfmod_compliant': mfmod_compliant,
                'export_ratio': export_ratio,
                'import_ratio': import_ratio,
                'export_compliant': export_compliant,
                'import_compliant': import_compliant
            }
            
        except Exception as e:
            return {'mfmod_compliant': False, 'error': str(e)}
