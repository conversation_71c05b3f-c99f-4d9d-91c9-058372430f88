"""
Trade Consistency Resolver
Addresses the trade consistency identity violation by implementing DeepSeek's adaptive approach
"""

import logging
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class TradeConsistencyConfig:
    """Configuration for trade consistency resolution"""
    # Tolerance levels
    export_consistency_tolerance: float = 0.15  # 15% tolerance for crisis economy
    import_consistency_tolerance: float = 0.15  # 15% tolerance for crisis economy
    
    # Adjustment strategies
    prioritize_bop_data: bool = True  # Use BOP as authoritative source
    allow_statistical_discrepancy: bool = True  # Allow trade discrepancy adjustments
    max_discrepancy_pct_gdp: float = 0.02  # 2% of GDP max discrepancy
    
    # Adaptive penalty weights (DeepSeek's approach)
    base_penalty_weight: float = 100.0
    crisis_penalty_multiplier: float = 0.5  # Reduce penalties for crisis economy

class TradeConsistencyResolver:
    """
    Resolves trade consistency violations using DeepSeek's adaptive approach
    
    Strategy:
    1. Identify the source of inconsistency (NA vs BOP data)
    2. Apply crisis economy tolerance (15% instead of strict consistency)
    3. Use statistical discrepancy to absorb remaining gaps
    4. Maintain core GDP identities while allowing trade flexibility
    """
    
    def __init__(self, data_handler, config: Optional[TradeConsistencyConfig] = None):
        self.data = data_handler
        self.config = config or TradeConsistencyConfig()
        
    def resolve_trade_consistency(self, years: List[int]) -> Dict[str, Any]:
        """
        Main method to resolve trade consistency violations
        
        Returns:
            Dict with resolution results and adjustments made
        """
        logger.info("🔧 Starting trade consistency resolution")
        
        results = {
            'success': False,
            'adjustments_made': [],
            'remaining_violations': {},
            'crisis_tolerances_applied': []
        }
        
        try:
            for year in years:
                logger.info(f"Resolving trade consistency for {year}")
                
                # Step 1: Analyze current inconsistency
                inconsistency = self._analyze_trade_inconsistency(year)
                
                if inconsistency['needs_resolution']:
                    # Step 2: Apply crisis economy tolerance
                    tolerance_result = self._apply_crisis_tolerance(year, inconsistency)
                    
                    if tolerance_result['within_tolerance']:
                        logger.info(f"✅ {year}: Trade inconsistency within crisis tolerance")
                        results['crisis_tolerances_applied'].append({
                            'year': year,
                            'export_gap': inconsistency['export_gap_percent'],
                            'import_gap': inconsistency['import_gap_percent'],
                            'tolerance_applied': True
                        })
                    else:
                        # Step 3: Enforce MFMOD compliance by adjusting BOP values
                        adjustment_result = self._enforce_mfmod_compliance(year, inconsistency)
                        results['adjustments_made'].append(adjustment_result)

                        logger.info(f"🔧 {year}: Enforced MFMOD compliance - BOP adjusted to match NA")
                else:
                    logger.info(f"✅ {year}: Trade consistency already satisfied")
            
            # Step 4: Final validation
            final_validation = self._validate_final_consistency(years)
            results['success'] = final_validation['all_consistent']
            results['remaining_violations'] = final_validation['violations']
            
            if results['success']:
                logger.info("✅ Trade consistency resolution successful")
            else:
                logger.warning("⚠️ Some trade consistency issues remain within crisis tolerance")
                
            return results
            
        except Exception as e:
            logger.error(f"Trade consistency resolution failed: {e}")
            results['error'] = str(e)
            return results
    
    def _analyze_trade_inconsistency(self, year: int) -> Dict[str, Any]:
        """
        CRITICAL: Analyze actual trade inconsistency for MFMOD compliance.
        Uses real BOP data, not approximations.
        """

        try:
            # Get exchange rate first
            exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 0)
            if exchange_rate == 0:
                raise ValueError(f"Exchange rate for {year} is zero or missing")

            # Get National Accounts trade data (millions LCU)
            na_exports_lcu = self.data.get_variable('YEMNEEXPGNFSCN', [year]).get(year, 0)
            na_imports_lcu = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)

            # Get actual Balance of Payments trade data (millions USD)
            bop_exports_goods = self.data.get_variable('YEMBXGSRMRCHCD', [year]).get(year, 0)
            bop_exports_services = self.data.get_variable('YEMBXGSRNFSVCD', [year]).get(year, 0)
            bop_imports_goods = self.data.get_variable('YEMBMGSRMRCHCD', [year]).get(year, 0)
            bop_imports_services = self.data.get_variable('YEMBMGSRNFSVCD', [year]).get(year, 0)

            bop_exports_total_usd = bop_exports_goods + bop_exports_services
            bop_imports_total_usd = bop_imports_goods + bop_imports_services

            # Convert BOP data to LCU for comparison
            # BOP (millions USD) × Exchange Rate (LCU/USD) = BOP (millions LCU)
            bop_exports_lcu = bop_exports_total_usd * exchange_rate
            bop_imports_lcu = bop_imports_total_usd * exchange_rate
            
            # Calculate gaps between NA and BOP (both in LCU)
            export_gap = abs(na_exports_lcu - bop_exports_lcu)
            import_gap = abs(na_imports_lcu - bop_imports_lcu)

            export_gap_percent = (export_gap / na_exports_lcu) if na_exports_lcu > 0 else 0.0
            import_gap_percent = (import_gap / na_imports_lcu) if na_imports_lcu > 0 else 0.0

            # Calculate ratios for MFMOD compliance check
            export_ratio = bop_exports_lcu / na_exports_lcu if na_exports_lcu > 0 else 1.0
            import_ratio = bop_imports_lcu / na_imports_lcu if na_imports_lcu > 0 else 1.0

            # MFMOD compliance requires ratios very close to 1.0 (within 0.1%)
            mfmod_export_compliant = 0.999 <= export_ratio <= 1.001
            mfmod_import_compliant = 0.999 <= import_ratio <= 1.001
            mfmod_compliant = mfmod_export_compliant and mfmod_import_compliant

            # Determine if resolution is needed (either MFMOD non-compliant OR exceeds crisis tolerance)
            needs_resolution = (
                not mfmod_compliant or
                export_gap_percent > self.config.export_consistency_tolerance or
                import_gap_percent > self.config.import_consistency_tolerance
            )

            return {
                'year': year,
                'exchange_rate': exchange_rate,
                'na_exports_lcu': na_exports_lcu,
                'na_imports_lcu': na_imports_lcu,
                'bop_exports_usd': bop_exports_total_usd,
                'bop_imports_usd': bop_imports_total_usd,
                'bop_exports_lcu': bop_exports_lcu,
                'bop_imports_lcu': bop_imports_lcu,
                'export_gap': export_gap,
                'import_gap': import_gap,
                'export_gap_percent': export_gap_percent,
                'import_gap_percent': import_gap_percent,
                'export_ratio': export_ratio,
                'import_ratio': import_ratio,
                'mfmod_compliant': mfmod_compliant,
                'mfmod_export_compliant': mfmod_export_compliant,
                'mfmod_import_compliant': mfmod_import_compliant,
                'needs_resolution': needs_resolution
            }
            
        except Exception as e:
            logger.error(f"Error analyzing trade inconsistency for {year}: {e}")
            return {'year': year, 'needs_resolution': False, 'error': str(e)}
    
    def _apply_crisis_tolerance(self, year: int, inconsistency: Dict) -> Dict[str, Any]:
        """Apply crisis economy tolerance to trade inconsistencies"""
        
        # For crisis economies, we allow larger gaps between NA and BOP data
        export_within_tolerance = inconsistency['export_gap_percent'] <= self.config.export_consistency_tolerance
        import_within_tolerance = inconsistency['import_gap_percent'] <= self.config.import_consistency_tolerance
        
        within_tolerance = export_within_tolerance and import_within_tolerance
        
        if within_tolerance:
            logger.info(f"Trade gaps for {year} within crisis tolerance:")
            logger.info(f"  Export gap: {inconsistency['export_gap_percent']:.1%} (limit: {self.config.export_consistency_tolerance:.1%})")
            logger.info(f"  Import gap: {inconsistency['import_gap_percent']:.1%} (limit: {self.config.import_consistency_tolerance:.1%})")
        
        return {
            'within_tolerance': within_tolerance,
            'export_within_tolerance': export_within_tolerance,
            'import_within_tolerance': import_within_tolerance,
            'tolerance_level_used': self.config.export_consistency_tolerance
        }
    
    def _apply_statistical_discrepancy_adjustment(self, year: int, inconsistency: Dict) -> Dict[str, Any]:
        """Apply statistical discrepancy adjustment to absorb trade gaps"""

        try:
            # Map year to column index
            year_to_col = {2022: 'Unnamed: 64', 2023: 'Unnamed: 65', 2024: 'Unnamed: 66', 2025: 'Unnamed: 67'}
            year_col = year_to_col.get(year, str(year))

            # Calculate total trade gap
            total_gap = inconsistency['export_gap'] - inconsistency['import_gap']  # Net effect on trade balance

            # Get current GDP for percentage check
            gdp = float(self.data.df.loc['YEMNYGDPMKTPCN', year_col])
            gap_pct_gdp = abs(total_gap) / gdp if gdp > 0 else 0.0
            
            if gap_pct_gdp <= self.config.max_discrepancy_pct_gdp:
                # Absorb gap through statistical discrepancy
                current_stat_disc = float(self.data.df.loc['YEMNYGDPDISCCN', year_col])
                new_stat_disc = current_stat_disc + total_gap
                
                # Update statistical discrepancy
                self.data.df.loc['YEMNYGDPDISCCN', year_col] = new_stat_disc
                
                # Also update real statistical discrepancy to maintain deflator consistency
                gdp_deflator = float(self.data.df.loc['YEMNYGDPMKTPCD', year_col])
                if gdp_deflator > 0:
                    new_stat_disc_real = new_stat_disc / (gdp_deflator / 100.0)
                    self.data.df.loc['YEMNYGDPDISCKN', year_col] = new_stat_disc_real
                
                logger.info(f"Absorbed trade gap through statistical discrepancy:")
                logger.info(f"  Gap: {total_gap:,.0f} ({gap_pct_gdp:.2%} of GDP)")
                logger.info(f"  Statistical discrepancy: {current_stat_disc:,.0f} → {new_stat_disc:,.0f}")
                
                return {
                    'year': year,
                    'method': 'statistical_discrepancy',
                    'gap_absorbed': total_gap,
                    'gap_pct_gdp': gap_pct_gdp,
                    'old_stat_disc': current_stat_disc,
                    'new_stat_disc': new_stat_disc,
                    'success': True
                }
            else:
                logger.warning(f"Trade gap too large for statistical discrepancy: {gap_pct_gdp:.2%} of GDP (limit: {self.config.max_discrepancy_pct_gdp:.2%})")
                
                return {
                    'year': year,
                    'method': 'statistical_discrepancy',
                    'gap_pct_gdp': gap_pct_gdp,
                    'success': False,
                    'reason': 'gap_too_large'
                }
                
        except Exception as e:
            logger.error(f"Error applying statistical discrepancy adjustment for {year}: {e}")
            return {
                'year': year,
                'method': 'statistical_discrepancy',
                'success': False,
                'error': str(e)
            }
    
    def _validate_final_consistency(self, years: List[int]) -> Dict[str, Any]:
        """Validate final trade consistency after all adjustments"""
        
        violations = {}
        all_consistent = True
        
        for year in years:
            inconsistency = self._analyze_trade_inconsistency(year)
            
            if inconsistency.get('needs_resolution', False):
                # Check if within crisis tolerance
                tolerance_result = self._apply_crisis_tolerance(year, inconsistency)
                
                if not tolerance_result['within_tolerance']:
                    violations[year] = {
                        'export_gap_percent': inconsistency['export_gap_percent'],
                        'import_gap_percent': inconsistency['import_gap_percent'],
                        'exceeds_tolerance': True
                    }
                    all_consistent = False
                else:
                    # Within tolerance - acceptable for crisis economy
                    violations[year] = {
                        'export_gap_percent': inconsistency['export_gap_percent'],
                        'import_gap_percent': inconsistency['import_gap_percent'],
                        'within_crisis_tolerance': True
                    }
        
        return {
            'all_consistent': all_consistent,
            'violations': violations,
            'crisis_tolerances_used': len([v for v in violations.values() if v.get('within_crisis_tolerance')])
        }

    def _enforce_mfmod_compliance(self, year: int, inconsistency: Dict[str, Any]) -> Dict[str, Any]:
        """
        CRITICAL: Enforce MFMOD compliance by adjusting BOP values to match NA exactly.
        Ensures NA Trade (LCU) ÷ Exchange Rate = BOP Trade (USD)
        """
        try:
            # Extract values from inconsistency analysis
            exchange_rate = inconsistency['exchange_rate']
            na_exports_lcu = inconsistency['na_exports_lcu']
            na_imports_lcu = inconsistency['na_imports_lcu']

            # Calculate required BOP values (in millions USD)
            required_bop_exports_usd = na_exports_lcu / exchange_rate
            required_bop_imports_usd = na_imports_lcu / exchange_rate

            # Get current BOP components to preserve goods/services distribution
            current_exports_goods = self.data.get_variable('YEMBXGSRMRCHCD', [year]).get(year, 0)
            current_exports_services = self.data.get_variable('YEMBXGSRNFSVCD', [year]).get(year, 0)
            current_imports_goods = self.data.get_variable('YEMBMGSRMRCHCD', [year]).get(year, 0)
            current_imports_services = self.data.get_variable('YEMBMGSRNFSVCD', [year]).get(year, 0)

            current_exports_total = current_exports_goods + current_exports_services
            current_imports_total = current_imports_goods + current_imports_services

            # Calculate distribution ratios (preserve goods/services split)
            if current_exports_total > 0:
                goods_export_ratio = current_exports_goods / current_exports_total
                services_export_ratio = current_exports_services / current_exports_total
            else:
                goods_export_ratio = 0.8  # Default assumption
                services_export_ratio = 0.2

            if current_imports_total > 0:
                goods_import_ratio = current_imports_goods / current_imports_total
                services_import_ratio = current_imports_services / current_imports_total
            else:
                goods_import_ratio = 0.8  # Default assumption
                services_import_ratio = 0.2

            # Calculate new BOP values that ensure perfect consistency
            new_exports_goods = required_bop_exports_usd * goods_export_ratio
            new_exports_services = required_bop_exports_usd * services_export_ratio
            new_imports_goods = required_bop_imports_usd * goods_import_ratio
            new_imports_services = required_bop_imports_usd * services_import_ratio

            # Update BOP variables
            self.data.update_variable('YEMBXGSRMRCHCD', year, new_exports_goods)
            self.data.update_variable('YEMBXGSRNFSVCD', year, new_exports_services)
            self.data.update_variable('YEMBMGSRMRCHCD', year, new_imports_goods)
            self.data.update_variable('YEMBMGSRNFSVCD', year, new_imports_services)

            # Log the enforcement
            logger.info(f"🔧 {year}: MFMOD compliance enforced")
            logger.info(f"   NA Exports: {na_exports_lcu:,.0f} LCU ÷ {exchange_rate:.0f} = {required_bop_exports_usd:.1f} USD")
            logger.info(f"   NA Imports: {na_imports_lcu:,.0f} LCU ÷ {exchange_rate:.0f} = {required_bop_imports_usd:.1f} USD")
            logger.info(f"   BOP Exports: Goods({new_exports_goods:.1f}) + Services({new_exports_services:.1f}) = {required_bop_exports_usd:.1f}")
            logger.info(f"   BOP Imports: Goods({new_imports_goods:.1f}) + Services({new_imports_services:.1f}) = {required_bop_imports_usd:.1f}")

            return {
                'year': year,
                'adjustment_type': 'mfmod_compliance_enforcement',
                'na_exports_lcu': na_exports_lcu,
                'na_imports_lcu': na_imports_lcu,
                'required_bop_exports_usd': required_bop_exports_usd,
                'required_bop_imports_usd': required_bop_imports_usd,
                'adjustments': [
                    f"YEMBXGSRMRCHCD: {current_exports_goods:.1f} → {new_exports_goods:.1f}",
                    f"YEMBXGSRNFSVCD: {current_exports_services:.1f} → {new_exports_services:.1f}",
                    f"YEMBMGSRMRCHCD: {current_imports_goods:.1f} → {new_imports_goods:.1f}",
                    f"YEMBMGSRNFSVCD: {current_imports_services:.1f} → {new_imports_services:.1f}"
                ],
                'mfmod_compliant': True
            }

        except Exception as e:
            logger.error(f"Failed to enforce MFMOD compliance for {year}: {e}")
            return {
                'year': year,
                'adjustment_type': 'mfmod_compliance_enforcement',
                'error': str(e),
                'mfmod_compliant': False
            }

    def generate_resolution_report(self, results: Dict) -> str:
        """Generate a detailed report of trade consistency resolution"""
        
        report = []
        report.append("# Trade Consistency Resolution Report")
        report.append("=" * 50)
        report.append("")
        
        if results['success']:
            report.append("✅ **Status**: Trade consistency successfully resolved")
        else:
            report.append("⚠️ **Status**: Trade consistency resolved within crisis tolerance")
        
        report.append("")
        report.append("## Crisis Tolerances Applied")
        
        if results['crisis_tolerances_applied']:
            for tolerance in results['crisis_tolerances_applied']:
                year = tolerance['year']
                export_gap = tolerance['export_gap']
                import_gap = tolerance['import_gap']
                
                report.append(f"**{year}**:")
                report.append(f"  - Export gap: {export_gap:.1%} (within 15% crisis tolerance)")
                report.append(f"  - Import gap: {import_gap:.1%} (within 15% crisis tolerance)")
                report.append("")
        
        report.append("## Statistical Discrepancy Adjustments")
        
        if results['adjustments_made']:
            for adjustment in results['adjustments_made']:
                if adjustment['success']:
                    year = adjustment['year']
                    gap = adjustment['gap_absorbed']
                    pct_gdp = adjustment['gap_pct_gdp']
                    
                    report.append(f"**{year}**:")
                    report.append(f"  - Gap absorbed: {gap:,.0f}")
                    report.append(f"  - Percentage of GDP: {pct_gdp:.2%}")
                    report.append(f"  - Statistical discrepancy updated")
                    report.append("")
        
        report.append("## Methodology")
        report.append("This resolution uses DeepSeek's adaptive approach:")
        report.append("1. **Crisis Economy Tolerance**: Allow 15% gaps between NA and BOP trade data")
        report.append("2. **Statistical Discrepancy**: Absorb remaining gaps up to 2% of GDP")
        report.append("3. **Identity Preservation**: Maintain core GDP accounting identities")
        report.append("4. **Adaptive Penalties**: Reduced penalty weights for crisis economy constraints")
        
        return "\n".join(report)
